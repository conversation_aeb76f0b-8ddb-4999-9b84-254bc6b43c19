require "test_helper"

class DashboardAnalyticsServiceTest < ActiveSupport::TestCase
  setup do
    @tenant = tenants(:one)
    @user = users(:owner_one)
    @service = DashboardAnalyticsService.for_tenant(@tenant, @user)
  end

  test "should initialize with tenant and user" do
    assert_equal @tenant, @service.instance_variable_get(:@tenant)
    assert_equal @user, @service.instance_variable_get(:@user)
  end

  test "should return dashboard metrics" do
    metrics = @service.dashboard_metrics
    
    assert_kind_of Hash, metrics
    assert_includes metrics.keys, :overview
    assert_includes metrics.keys, :document_processing
    assert_includes metrics.keys, :user_activity
    assert_includes metrics.keys, :billing
    assert_includes metrics.keys, :system_performance
    assert_includes metrics.keys, :trends
  end

  test "should return overview metrics" do
    overview = @service.overview_metrics
    
    assert_kind_of Hash, overview
    assert_includes overview.keys, :total_documents
    assert_includes overview.keys, :documents_this_period
    assert_includes overview.keys, :total_templates
    assert_includes overview.keys, :active_templates
    assert_includes overview.keys, :total_users
    assert_includes overview.keys, :active_users
    assert_includes overview.keys, :success_rate
    assert_includes overview.keys, :avg_processing_time
  end

  test "should return document processing metrics" do
    processing = @service.document_processing_metrics
    
    assert_kind_of Hash, processing
    assert_includes processing.keys, :total_processed
    assert_includes processing.keys, :pending
    assert_includes processing.keys, :processing
    assert_includes processing.keys, :failed
    assert_includes processing.keys, :success_rate
    assert_includes processing.keys, :failure_rate
    assert_includes processing.keys, :avg_processing_time
  end

  test "should return user activity metrics" do
    activity = @service.user_activity_metrics
    
    assert_kind_of Hash, activity
    assert_includes activity.keys, :active_users_today
    assert_includes activity.keys, :active_users_this_week
    assert_includes activity.keys, :documents_uploaded_today
    assert_includes activity.keys, :documents_uploaded_this_week
    assert_includes activity.keys, :user_engagement
  end

  test "should return billing metrics for authorized users" do
    billing = @service.billing_metrics
    
    assert_kind_of Hash, billing
    # Should have billing data for owners
    assert_includes billing.keys, :subscription_status
    assert_includes billing.keys, :plan_name
  end

  test "should return empty billing metrics for unauthorized users" do
    member_user = users(:member_one)
    service = DashboardAnalyticsService.for_tenant(@tenant, member_user)
    billing = service.billing_metrics
    
    assert_empty billing
  end

  test "should return system performance metrics" do
    performance = @service.system_performance_metrics
    
    assert_kind_of Hash, performance
    assert_includes performance.keys, :queue_stats
    assert_includes performance.keys, :api_performance
    assert_includes performance.keys, :error_rates
    assert_includes performance.keys, :processing_efficiency
  end

  test "should return trend metrics" do
    trends = @service.trend_metrics
    
    assert_kind_of Hash, trends
    assert_includes trends.keys, :document_trends
    assert_includes trends.keys, :success_rate_trends
    assert_includes trends.keys, :user_growth_trends
    assert_includes trends.keys, :performance_trends
  end

  test "should handle custom date range" do
    date_range = 7.days.ago.beginning_of_day..Time.current.end_of_day
    service = DashboardAnalyticsService.for_tenant(@tenant, @user, date_range)
    
    assert_equal date_range, service.instance_variable_get(:@date_range)
  end

  test "should cache dashboard metrics" do
    # First call should hit the database
    metrics1 = @service.dashboard_metrics

    # Second call should return the same structure
    metrics2 = @service.dashboard_metrics

    # Verify both calls return the same structure
    assert_equal metrics1.keys.sort, metrics2.keys.sort
    assert_kind_of Hash, metrics1
    assert_kind_of Hash, metrics2
  end

  test "should calculate success rate correctly" do
    # Create test documents
    @tenant.documents.create!(
      name: "Test Doc 1",
      status: "completed",
      user: @user,
      created_at: 1.day.ago
    )
    
    @tenant.documents.create!(
      name: "Test Doc 2", 
      status: "failed",
      user: @user,
      created_at: 1.day.ago
    )
    
    overview = @service.overview_metrics
    
    # Should calculate success rate based on completed vs total
    assert overview[:success_rate] >= 0
    assert overview[:success_rate] <= 100
  end

  test "should handle empty data gracefully" do
    # Test with tenant that has no data
    empty_tenant = Tenant.create!(
      name: "Empty Tenant",
      subdomain: "empty-test"
    )
    
    service = DashboardAnalyticsService.for_tenant(empty_tenant, @user)
    metrics = service.dashboard_metrics
    
    assert_kind_of Hash, metrics
    assert_equal 0, metrics[:overview][:total_documents]
    assert_equal 0, metrics[:overview][:success_rate]
  end
end
