require "test_helper"

class WebhookDeliveryJobTest < ActiveJob::TestCase
  setup do
    @tenant = tenants(:one)
    @user = users(:owner_one)
    @webhook = Webhook.create!(
      tenant: @tenant,
      user: @user,
      name: "Test Webhook",
      url: "http://localhost:9999/webhook", # Use localhost to avoid real HTTP calls
      events: ["document.created", "document.processed"],
      active: true
    )
    @payload = {
      id: "doc_123",
      name: "Test Document",
      status: "processed"
    }
  end
  
  test "job enqueues correctly" do
    assert_enqueued_with(job: WebhookDeliveryJob) do
      WebhookDeliveryJob.perform_later(@webhook, "document.created", @payload)
    end
  end
  
  test "job uses correct queue" do
    assert_equal "webhooks", WebhookDeliveryJob.new.queue_name
  end
  
  test "webhook trigger method does not call job for inactive webhooks" do
    @webhook.update!(active: false)
    
    # The trigger method should not enqueue the job for inactive webhooks
    assert_no_enqueued_jobs do
      @webhook.trigger("document.created", @payload)
    end
  end
  
  test "creates webhook event with correct attributes" do
    # Create the webhook event directly to test the structure
    webhook_event = @webhook.webhook_events.create!(
      event_type: "document.created",
      payload: @payload,
      status: "pending"
    )
    
    assert_equal @webhook, webhook_event.webhook
    assert_equal "document.created", webhook_event.event_type
    assert_equal @payload.stringify_keys, webhook_event.payload
    assert_equal "pending", webhook_event.status
  end
  
  test "webhook event can transition through statuses" do
    webhook_event = @webhook.webhook_events.create!(
      event_type: "document.created",
      payload: @payload,
      status: "pending"
    )
    
    # Test status transitions
    assert webhook_event.can_deliver?
    
    webhook_event.update!(status: "delivering")
    assert_not webhook_event.can_deliver?
    
    webhook_event.update!(status: "delivered", delivered_at: Time.current)
    assert webhook_event.success?
    assert_not webhook_event.failed?
    
    webhook_event.update!(status: "failed", error_message: "Connection refused")
    assert webhook_event.failed?
    assert_not webhook_event.success?
  end
  
  test "webhook event retry logic" do
    webhook_event = @webhook.webhook_events.create!(
      event_type: "document.created",
      payload: @payload,
      status: "failed",
      attempt_count: 1
    )
    
    assert webhook_event.should_retry?
    
    # Test exponential backoff calculation
    webhook_event.update!(attempt_count: 1)
    assert_equal 30, webhook_event.send(:retry_delay_seconds)
    
    webhook_event.update!(attempt_count: 2)
    assert_equal 60, webhook_event.send(:retry_delay_seconds)
    
    webhook_event.update!(attempt_count: 3)
    assert_equal 120, webhook_event.send(:retry_delay_seconds)
    
    # Should not retry after max attempts
    webhook_event.update!(attempt_count: @webhook.retry_count)
    assert_not webhook_event.should_retry?
  end
  
  test "webhook signs payload correctly" do
    signature = @webhook.sign_payload(@payload)
    assert_not_nil signature
    assert_equal 64, signature.length # SHA256 hex digest length
    
    # Verify signature
    assert @webhook.verify_signature(@payload, signature)
    assert_not @webhook.verify_signature(@payload, "invalid_signature")
  end
end