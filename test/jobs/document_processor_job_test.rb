require "test_helper"

class DocumentProcessorJobTest < ActiveJob::TestCase
  setup do
    @tenant = tenants(:one)
    @user = users(:owner_one)
    @template = extraction_templates(:invoice_template)
    @document = documents(:pending_invoice)
    
    # Default extracted data for tests
    @extracted_data = {
      "invoice_number" => "INV-2024-001",
      "date" => "2024-01-15",
      "total_amount" => "1250.00",
      "vendor_name" => "Test Vendor Inc"
    }
  end
  
  test "job uses correct queue based on document priority" do
    # Test critical priority
    @document.update!(priority: :critical)
    assert_equal "urgent", DocumentProcessorJob.new(@document).queue_name
    
    # Test high priority
    @document.update!(priority: :high)
    assert_equal "high_priority", DocumentProcessorJob.new(@document).queue_name
    
    # Test normal priority
    @document.update!(priority: :normal)
    assert_equal "document_processing", DocumentProcessorJob.new(@document).queue_name
  end
  
  test "completes document without extraction when no template" do
    @document.update!(extraction_template: nil)
    
    assert_changes -> { @document.reload.status }, from: "pending", to: "completed" do
      DocumentProcessorJob.perform_now(@document)
    end
  end
  
  test "job enqueues correctly" do
    assert_enqueued_with(job: DocumentProcessorJob, args: [@document]) do
      DocumentProcessorJob.perform_later(@document)
    end
  end
  
  test "uses tenant context during processing" do
    # Verify tenant is set in Current context
    @document.update!(extraction_template: nil) # Skip AI processing
    
    DocumentProcessorJob.perform_now(@document)
    
    # The document should be processed within the tenant context
    assert_equal "completed", @document.reload.status
  end
  
  test "document processor job exists and includes necessary methods" do
    job = DocumentProcessorJob.new
    
    # Test that the job responds to the perform method
    assert_respond_to job, :perform
    
    # Test queue_as block exists
    assert_equal "document_processing", DocumentProcessorJob.new(@document).queue_name
  end
  
  test "provider determination logic works correctly" do
    job = DocumentProcessorJob.new
    
    # Test with assigned model
    @document.update!(assigned_model: :gpt4_vision)
    provider = job.send(:determine_provider, @document.reload)
    assert_equal :openai, provider
    
    @document.update!(assigned_model: :claude_vision)
    provider = job.send(:determine_provider, @document.reload)
    assert_equal :anthropic, provider
    
    @document.update!(assigned_model: :fallback)
    provider = job.send(:determine_provider, @document.reload)
    assert_equal :google, provider
    
    # Test with template settings
    @document.update!(assigned_model: nil)
    @template.update!(settings: { 'ai_provider' => 'anthropic' })
    @document.reload
    provider = job.send(:determine_provider, @document)
    assert_equal :anthropic, provider
    
    # Test with tenant settings
    @template.update!(settings: {})
    @document.extraction_template.reload
    @tenant.update!(settings: @tenant.settings.merge('default_ai_provider' => 'google'))
    @document.reload
    provider = job.send(:determine_provider, @document)
    assert_equal :google, provider
    
    # Test default
    @tenant.update!(settings: {})
    @document.tenant.reload
    provider = job.send(:determine_provider, @document.reload)
    assert_equal :openai, provider
  end
  
  test "model determination logic works correctly" do
    job = DocumentProcessorJob.new
    
    # Test with assigned model
    @document.update!(assigned_model: :gpt4_vision)
    model = job.send(:determine_model, @document.reload, :openai)
    assert_equal 'gpt-4-turbo-vision', model
    
    @document.update!(assigned_model: :gpt4_turbo)
    model = job.send(:determine_model, @document.reload, :openai)
    assert_equal 'gpt-4-turbo', model
    
    @document.update!(assigned_model: :claude_vision)
    model = job.send(:determine_model, @document.reload, :anthropic)
    assert_equal 'claude-3-opus-20240229', model
    
    @document.update!(assigned_model: :fallback)
    model = job.send(:determine_model, @document.reload, :google)
    assert_equal 'gemini-1.5-flash', model
    
    # Test with template settings
    @document.update!(assigned_model: nil)
    @template.update!(settings: { 'ai_model' => 'custom-model' })
    @document.reload
    model = job.send(:determine_model, @document, :openai)
    assert_equal 'custom-model', model
    
    # Test provider defaults
    @template.update!(settings: {})
    @document.extraction_template.reload
    model = job.send(:determine_model, @document.reload, :openai)
    assert_equal 'gpt-4o-mini', model
    
    model = job.send(:determine_model, @document, :anthropic)
    assert_equal 'claude-3-haiku-20240307', model
    
    model = job.send(:determine_model, @document, :google)
    assert_equal 'gemini-1.5-flash', model
    
    model = job.send(:determine_model, @document, :deepseek)
    assert_equal 'deepseek-chat', model
  end
end