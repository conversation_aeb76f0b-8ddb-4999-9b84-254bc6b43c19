require "test_helper"

class UserBillingTest < ActiveSupport::TestCase
  setup do
    @tenant = tenants(:one)
    @user = users(:owner_one)
  end
  
  test "user delegates subscription methods to tenant" do
    assert_equal @tenant.subscribed?, @user.subscribed?
    assert_equal @tenant.subscription, @user.subscription
    assert_equal @tenant.on_trial?, @user.on_trial?
    assert_equal @tenant.plan_name, @user.plan_name
  end
  
  test "user delegates feature checks to tenant" do
    assert_equal @tenant.has_feature?(:api_access), @user.has_feature?(:api_access)
  end
  
  test "user delegates plan limits to tenant" do
    assert_equal @tenant.plan_limit(:documents_per_month), @user.plan_limit(:documents_per_month)
    assert_equal @tenant.at_plan_limit?(:team_members), @user.at_plan_limit?(:team_members)
  end
  
  test "only owners can manage billing" do
    @user.update(role: :owner)
    assert @user.can_manage_billing?
    
    @user.update(role: :admin)
    assert_not @user.can_manage_billing?
    
    @user.update(role: :member)
    assert_not @user.can_manage_billing?
  end
end