require "test_helper"

class DashboardMetricTest < ActiveSupport::TestCase
  def setup
    @tenant = tenants(:one)
  end

  test "should create dashboard metric with valid attributes" do
    metric = DashboardMetric.new(
      tenant: @tenant,
      metric_type: 'document_count',
      time_period: 'daily',
      value: 100,
      recorded_at: Time.current
    )
    
    assert metric.valid?
    assert metric.save
  end

  test "should require tenant" do
    metric = DashboardMetric.new(
      metric_type: 'document_count',
      time_period: 'daily',
      value: 100,
      recorded_at: Time.current
    )
    
    assert_not metric.valid?
    assert_includes metric.errors[:tenant], "must exist"
  end

  test "should require metric_type" do
    metric = DashboardMetric.new(
      tenant: @tenant,
      time_period: 'daily',
      value: 100,
      recorded_at: Time.current
    )
    
    assert_not metric.valid?
    assert_includes metric.errors[:metric_type], "can't be blank"
  end

  test "should validate metric_type inclusion" do
    metric = DashboardMetric.new(
      tenant: @tenant,
      metric_type: 'invalid_type',
      time_period: 'daily',
      value: 100,
      recorded_at: Time.current
    )
    
    assert_not metric.valid?
    assert_includes metric.errors[:metric_type], "is not included in the list"
  end

  test "should validate time_period inclusion" do
    metric = DashboardMetric.new(
      tenant: @tenant,
      metric_type: 'document_count',
      time_period: 'invalid_period',
      value: 100,
      recorded_at: Time.current
    )
    
    assert_not metric.valid?
    assert_includes metric.errors[:time_period], "is not included in the list"
  end

  test "should require numeric value" do
    metric = DashboardMetric.new(
      tenant: @tenant,
      metric_type: 'document_count',
      time_period: 'daily',
      value: 'not_a_number',
      recorded_at: Time.current
    )
    
    assert_not metric.valid?
    assert_includes metric.errors[:value], "is not a number"
  end

  test "should record metric using class method" do
    assert_difference 'DashboardMetric.count', 1 do
      DashboardMetric.record_metric(@tenant, 'document_count', 50)
    end
    
    metric = DashboardMetric.last
    assert_equal @tenant, metric.tenant
    assert_equal 'document_count', metric.metric_type
    assert_equal 50, metric.value
  end

  test "should calculate average for period" do
    # Create test metrics
    base_time = 3.days.ago
    DashboardMetric.create!(tenant: @tenant, metric_type: 'success_rate', time_period: 'daily', value: 80, recorded_at: base_time)
    DashboardMetric.create!(tenant: @tenant, metric_type: 'success_rate', time_period: 'daily', value: 90, recorded_at: base_time + 1.day)
    DashboardMetric.create!(tenant: @tenant, metric_type: 'success_rate', time_period: 'daily', value: 85, recorded_at: base_time + 2.days)
    
    average = DashboardMetric.average_for_period('success_rate', base_time, base_time + 2.days)
    assert_equal 85.0, average
  end

  test "should get trend data" do
    # Create test metrics over several days
    5.times do |i|
      DashboardMetric.create!(
        tenant: @tenant,
        metric_type: 'document_count',
        time_period: 'daily',
        value: (i + 1) * 10,
        recorded_at: i.days.ago.end_of_day
      )
    end
    
    trend_data = DashboardMetric.trend_data('document_count', days: 5)
    assert_equal 5, trend_data.length
    assert trend_data.all? { |point| point.key?(:date) && point.key?(:value) }
  end

  test "should format percentage values correctly" do
    metric = DashboardMetric.new(metric_type: 'success_rate', value: 85.5)
    assert_equal "85.5%", metric.formatted_value
  end

  test "should format currency values correctly" do
    metric = DashboardMetric.new(metric_type: 'revenue', value: 1234.56)
    assert_equal "$1234.56", metric.formatted_value
  end

  test "should format time values correctly" do
    metric = DashboardMetric.new(metric_type: 'processing_time', value: 2.345)
    assert_equal "2.35s", metric.formatted_value
  end

  test "should format regular values correctly" do
    metric = DashboardMetric.new(metric_type: 'document_count', value: 123.7)
    assert_equal "123", metric.formatted_value
  end

  test "should scope by type" do
    DashboardMetric.create!(tenant: @tenant, metric_type: 'document_count', time_period: 'daily', value: 10, recorded_at: Time.current)
    DashboardMetric.create!(tenant: @tenant, metric_type: 'success_rate', time_period: 'daily', value: 85, recorded_at: Time.current)
    
    document_metrics = DashboardMetric.for_type('document_count')
    assert_equal 1, document_metrics.count
    assert_equal 'document_count', document_metrics.first.metric_type
  end

  test "should scope by period" do
    DashboardMetric.create!(tenant: @tenant, metric_type: 'document_count', time_period: 'daily', value: 10, recorded_at: Time.current)
    DashboardMetric.create!(tenant: @tenant, metric_type: 'document_count', time_period: 'weekly', value: 70, recorded_at: Time.current)
    
    daily_metrics = DashboardMetric.for_period('daily')
    assert_equal 1, daily_metrics.count
    assert_equal 'daily', daily_metrics.first.time_period
  end
end
