require "test_helper"

class ExtractionTemplateSchemaTest < ActiveSupport::TestCase
  setup do
    @tenant = tenants(:one)
    @template = extraction_templates(:invoice_template)
  end
  
  test "generates valid JSON schema" do
    schema = @template.schema
    
    assert_equal "https://json-schema.org/draft/2020-12/schema", schema["$schema"]
    assert_equal @template.name, schema["title"]
    assert_equal "Schema for #{@template.document_type} extraction", schema["description"]
    assert_equal "object", schema["type"]
    assert schema["properties"].is_a?(Hash)
    assert schema["required"].is_a?(Array)
  end
  
  test "includes all fields in schema properties" do
    @template.update!(fields: [
      { "name" => "invoice_number", "type" => "string", "required" => true },
      { "name" => "amount", "type" => "number", "required" => true },
      { "name" => "date", "type" => "date", "required" => false }
    ])
    
    schema = @template.schema
    
    assert_equal 3, schema["properties"].size
    assert schema["properties"]["invoice_number"]
    assert schema["properties"]["amount"]
    assert schema["properties"]["date"]
  end
  
  test "correctly identifies required fields" do
    @template.update!(fields: [
      { "name" => "field1", "type" => "string", "required" => true },
      { "name" => "field2", "type" => "string", "required" => false },
      { "name" => "field3", "type" => "string", "required" => true }
    ])
    
    schema = @template.schema
    
    assert_equal ["field1", "field3"], schema["required"]
  end
  
  test "generates correct schema for string fields" do
    @template.update!(fields: [
      { 
        "name" => "text_field",
        "type" => "string",
        "description" => "A text field",
        "min_length" => 5,
        "max_length" => 100,
        "pattern" => "^[A-Z]",
        "required" => true
      }
    ])
    
    schema = @template.schema
    field_schema = schema["properties"]["text_field"]
    
    assert_equal "string", field_schema["type"]
    assert_equal "A text field", field_schema["description"]
    assert_equal 5, field_schema["minLength"]
    assert_equal 100, field_schema["maxLength"]
    assert_equal "^[A-Z]", field_schema["pattern"]
  end
  
  test "generates correct schema for number fields" do
    @template.update!(fields: [
      {
        "name" => "amount",
        "type" => "number",
        "description" => "Amount field",
        "min_value" => 0,
        "max_value" => 1000000,
        "required" => true
      }
    ])
    
    schema = @template.schema
    field_schema = schema["properties"]["amount"]
    
    assert_equal "number", field_schema["type"]
    assert_equal "Amount field", field_schema["description"]
    assert_equal 0, field_schema["minimum"]
    assert_equal 1000000, field_schema["maximum"]
  end
  
  test "generates correct schema for date fields" do
    @template.update!(fields: [
      {
        "name" => "invoice_date",
        "type" => "date",
        "description" => "Invoice date",
        "required" => true
      }
    ])
    
    schema = @template.schema
    field_schema = schema["properties"]["invoice_date"]
    
    assert_equal "string", field_schema["type"]
    assert_equal "date", field_schema["format"]
  end
  
  test "generates correct schema for array fields" do
    @template.update!(fields: [
      {
        "name" => "line_items",
        "type" => "array",
        "description" => "Line items",
        "item_type" => "object",
        "min_items" => 1,
        "max_items" => 100,
        "required" => false
      }
    ])
    
    schema = @template.schema
    field_schema = schema["properties"]["line_items"]
    
    assert_equal "array", field_schema["type"]
    assert_equal({ "type" => "object" }, field_schema["items"])
    assert_equal 1, field_schema["minItems"]
    assert_equal 100, field_schema["maxItems"]
  end
  
  test "generates correct schema for enum fields" do
    @template.update!(fields: [
      {
        "name" => "status",
        "type" => "string",
        "description" => "Invoice status",
        "allowed_values" => ["draft", "sent", "paid", "canceled"],
        "required" => true
      }
    ])
    
    schema = @template.schema
    field_schema = schema["properties"]["status"]
    
    assert_equal "string", field_schema["type"]
    assert_equal ["draft", "sent", "paid", "canceled"], field_schema["enum"]
  end
  
  test "export method returns complete template configuration" do
    export = @template.export
    
    assert_equal @template.name, export[:name]
    assert_equal @template.document_type, export[:document_type]
    assert_equal @template.description, export[:description]
    assert_equal @template.fields, export[:fields]
    assert_equal @template.prompt_template, export[:prompt_template]
    assert_equal @template.settings, export[:settings]
    assert_equal "1.0", export[:version]
    assert export[:exported_at]
  end
  
  test "import creates new template from configuration" do
    config = {
      name: "Imported Template",
      document_type: "invoice",
      description: "Test import",
      fields: [
        { "name" => "field1", "type" => "string", "required" => true }
      ],
      prompt_template: "Extract {{fields_list}}",
      settings: { "auto_approve" => false }
    }
    
    assert_difference "ExtractionTemplate.count", 1 do
      template = ExtractionTemplate.import(@tenant, config)
      assert_equal "Imported Template", template.name
      assert_equal "invoice", template.document_type
      assert_equal "Test import", template.description
      assert_equal config[:fields], template.fields
    end
  end
  
  test "import handles duplicate names" do
    existing_template = @tenant.extraction_templates.create!(
      name: "Duplicate Template",
      document_type: "invoice",
      fields: [{ "name" => "test", "type" => "string" }],
      prompt_template: "Test"
    )
    
    config = {
      name: "Duplicate Template",
      document_type: "invoice",
      fields: [{ "name" => "test", "type" => "string" }],
      prompt_template: "Test"
    }
    
    template = ExtractionTemplate.import(@tenant, config)
    assert_match /Duplicate Template \(imported/, template.name
    assert_not_equal existing_template.id, template.id
  end
end