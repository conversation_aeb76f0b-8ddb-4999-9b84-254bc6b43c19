require "test_helper"

class TenantSubscriptionTest < ActiveSupport::TestCase
  setup do
    @tenant = tenants(:one)
  end
  
  test "tenant includes pay customer functionality" do
    assert @tenant.respond_to?(:payment_processor)
    assert @tenant.respond_to?(:set_payment_processor)
  end
  
  test "subscribed? returns false when no subscription" do
    assert_not @tenant.subscribed?
  end
  
  test "plan_name returns Trial for trial tenants" do
    @tenant.update(plan: "trial", trial_ends_at: 30.days.from_now)
    assert_equal "Trial", @tenant.plan_name
  end
  
  test "on_trial? returns true during trial period" do
    @tenant.update(plan: "trial", trial_ends_at: 30.days.from_now)
    assert @tenant.on_trial?
  end
  
  test "on_trial? returns false after trial period" do
    @tenant.update(plan: "trial", trial_ends_at: 1.day.ago)
    assert_not @tenant.on_trial?
  end
  
  test "has_feature? returns true for trial tenants" do
    @tenant.update(plan: "trial", trial_ends_at: 30.days.from_now)
    assert @tenant.has_feature?(:api_access)
    assert @tenant.has_feature?(:priority_support)
  end
  
  test "plan_limit returns correct limits" do
    # Mock subscription with basic plan
    # In real tests, you would use VCR or mock the Pay gem
    assert_equal 100, @tenant.plan_limit(:documents_per_month)
    assert_equal 3, @tenant.plan_limit(:team_members)
  end
  
  test "at_plan_limit? checks against plan limits" do
    # The tenant already has one user from fixtures (owner_one)
    # Basic plan allows 3 team members
    
    # Add one more user (total will be 2)
    user = @tenant.users.build(email: "test#{SecureRandom.hex}@example.com", name: "Test User")
    user.password = "password123"
    user.skip_confirmation!
    user.save!
    
    assert_not @tenant.at_plan_limit?(:team_members) # Should have 2 users, limit is 3
    
    # Add one more to reach limit (total will be 3)
    user = @tenant.users.build(email: "test#{SecureRandom.hex}@example.com", name: "Test User")
    user.password = "password123"
    user.skip_confirmation!
    user.save!
    
    assert @tenant.at_plan_limit?(:team_members) # Should have 3 users, at limit
  end
  
  test "at_plan_limit? returns false for unlimited features" do
    # Mock enterprise plan with unlimited features
    # In real tests, would mock the subscription
    assert_not @tenant.at_plan_limit?(:documents_per_month)
  end
end