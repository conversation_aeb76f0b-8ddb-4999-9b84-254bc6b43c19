# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

owner_one:
  name: Owner User One
  email: <EMAIL>
  encrypted_password: <%= User.new.send(:password_digest, 'password123') %>
  tenant: one
  role: owner
  confirmed_at: <%= 1.day.ago %>
  sign_in_count: 5
  current_sign_in_at: <%= 1.hour.ago %>
  last_sign_in_at: <%= 2.hours.ago %>

admin_two:
  name: Admin User Two
  email: <EMAIL>
  encrypted_password: <%= User.new.send(:password_digest, 'password123') %>
  tenant: two
  role: admin
  confirmed_at: <%= 2.days.ago %>
  sign_in_count: 10
  current_sign_in_at: <%= 30.minutes.ago %>
  last_sign_in_at: <%= 1.day.ago %>

member_one:
  name: Member User One
  email: <EMAIL>
  encrypted_password: <%= User.new.send(:password_digest, 'password123') %>
  tenant: one
  role: member
  confirmed_at: <%= 3.days.ago %>
  sign_in_count: 3
  current_sign_in_at: <%= 2.hours.ago %>
  last_sign_in_at: <%= 1.day.ago %>
