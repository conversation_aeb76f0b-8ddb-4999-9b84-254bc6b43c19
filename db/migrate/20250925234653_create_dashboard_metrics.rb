class CreateDashboardMetrics < ActiveRecord::Migration[8.0]
  def change
    create_table :dashboard_metrics do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :metric_type, null: false
      t.string :time_period, null: false
      t.decimal :value, precision: 15, scale: 4, null: false
      t.datetime :recorded_at, null: false
      t.json :metadata, default: {}

      t.timestamps
    end

    # Indexes for performance
    add_index :dashboard_metrics, [:tenant_id, :metric_type, :time_period], name: 'idx_dashboard_metrics_tenant_type_period'
    add_index :dashboard_metrics, [:tenant_id, :recorded_at], name: 'idx_dashboard_metrics_tenant_date'
    add_index :dashboard_metrics, [:metric_type, :recorded_at], name: 'idx_dashboard_metrics_type_date'
    add_index :dashboard_metrics, :recorded_at

    # Unique constraint to prevent duplicate metrics for same time period
    add_index :dashboard_metrics, [:tenant_id, :metric_type, :time_period, :recorded_at],
              unique: true, name: 'idx_dashboard_metrics_unique'
  end
end
