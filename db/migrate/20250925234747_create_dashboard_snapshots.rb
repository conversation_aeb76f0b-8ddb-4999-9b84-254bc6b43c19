class CreateDashboardSnapshots < ActiveRecord::Migration[8.0]
  def change
    create_table :dashboard_snapshots do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :snapshot_type, null: false
      t.date :snapshot_date, null: false
      t.json :metrics_data, null: false, default: {}

      t.timestamps
    end

    # Indexes for performance
    add_index :dashboard_snapshots, [:tenant_id, :snapshot_type, :snapshot_date],
              unique: true, name: 'idx_dashboard_snapshots_unique'
    add_index :dashboard_snapshots, [:tenant_id, :snapshot_date], name: 'idx_dashboard_snapshots_tenant_date'
    add_index :dashboard_snapshots, :snapshot_date
  end
end
