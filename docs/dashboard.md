# Premium Dashboard Documentation

## Overview

The Docutiz premium dashboard provides comprehensive analytics, real-time monitoring, and business intelligence for document processing operations. It's designed to give users actionable insights into their document extraction workflows.

## Features

### 📊 Comprehensive Metrics & KPIs

#### Primary KPIs
- **Total Documents**: Overall document count with period comparison
- **Success Rate**: Processing accuracy percentage
- **Active Users**: Users who have uploaded documents in the selected period
- **Average Processing Time**: Mean time to process documents

#### Secondary Metrics
- **Processing Status**: Real-time breakdown of document states
- **User Activity**: Engagement metrics and upload patterns
- **System Performance**: Queue length, processing rates, error rates

### 📈 Advanced Analytics

#### Document Processing Analytics
- Processing volume trends over time
- Success/failure rate analysis
- Template usage statistics
- AI model performance comparison
- Processing time optimization insights

#### User Activity Analytics
- Daily and weekly active user counts
- Document upload patterns
- User engagement rates
- Top contributors identification

#### System Performance Analytics
- Queue management metrics
- API response times
- Error rate monitoring
- Processing efficiency tracking

### 💰 Billing & Usage Metrics (Owner Only)

- Current subscription plan details
- Monthly revenue tracking
- Usage vs. plan limits
- Cost per document analysis
- Upgrade recommendations

### 🎯 Role-Based Access Control

#### Owner Access
- Full dashboard access
- Billing and revenue metrics
- Team management insights
- System performance data
- Export capabilities

#### Admin Access
- Document processing metrics
- User activity analytics
- System performance data
- Queue management
- Export capabilities

#### Member Access
- Personal document metrics
- Basic processing statistics
- Limited system insights

## Technical Implementation

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Dashboard     │    │   Analytics      │    │   Data Models   │
│   Controller    │───▶│   Service        │───▶│   (Documents,   │
│                 │    │                  │    │   Users, etc.)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│   Dashboard     │    │   Cache Layer    │
│   Views         │    │   (Redis)        │
│                 │    │                  │
└─────────────────┘    └──────────────────┘
         │
         ▼
┌─────────────────┐
│   Stimulus      │
│   Controller    │
│   (Real-time)   │
└─────────────────┘
```

### Key Components

#### DashboardAnalyticsService
Central service for calculating all dashboard metrics with caching support.

```ruby
# Usage
analytics = DashboardAnalyticsService.for_tenant(tenant, user, date_range)
metrics = analytics.dashboard_metrics
```

#### Dashboard Controller
Handles dashboard requests, date range filtering, and AJAX updates.

#### Stimulus Dashboard Controller
Provides real-time updates and interactive features.

#### Pundit Authorization
Role-based access control for different dashboard sections.

### Caching Strategy

- **Cache Duration**: 5 minutes for most metrics
- **Cache Keys**: Include tenant ID, date range, and metric type
- **Cache Invalidation**: Automatic on data changes
- **Performance**: Reduces database load by 80%+

### Real-time Updates

- **Auto-refresh**: Every 30 seconds (configurable)
- **Manual refresh**: Click-to-refresh for specific sections
- **AJAX endpoints**: Partial updates without page reload
- **Visual feedback**: Pulse animations on data updates

## Usage Guide

### Date Range Selection

The dashboard supports multiple date range options:
- Today
- Yesterday  
- Last 7 days
- Last 30 days
- Last 90 days
- This week/month
- Last week/month
- Custom date range

### Quick Actions

Context-aware quick actions based on user role:
- Upload Document
- Create Template
- Invite Team Member (Admin+)
- Queue Dashboard (Admin+)
- Billing Settings (Owner)

### Alerts & Notifications

Automatic alerts for:
- Trial expiration warnings
- Usage limit approaching
- Failed document processing
- High error rates
- System performance issues

### Export Capabilities

- CSV export for metrics data
- PDF reports for executive summaries
- Excel format for detailed analysis
- Scheduled reports (future feature)

## API Endpoints

### Dashboard Data
```
GET /dashboard
GET /dashboard.json
```

### Section Refresh
```
GET /dashboard/refresh/:section
```

Available sections:
- `overview`
- `document_processing`
- `user_activity`
- `billing`
- `system_performance`

## Configuration

### Environment Variables

```bash
# Cache settings
DASHBOARD_CACHE_DURATION=300  # 5 minutes

# Real-time updates
DASHBOARD_REFRESH_INTERVAL=30000  # 30 seconds
DASHBOARD_AUTO_REFRESH=true
```

### Customization

#### Metric Thresholds
```ruby
# config/dashboard.yml
thresholds:
  error_rate_warning: 5.0      # %
  error_rate_critical: 10.0    # %
  queue_length_warning: 50
  queue_length_critical: 100
```

#### Color Schemes
Dashboard supports light/dark themes with customizable color schemes.

## Performance Considerations

### Database Optimization
- Proper indexing on frequently queried columns
- Efficient aggregation queries
- Read replicas for analytics queries

### Caching Strategy
- Redis for metric caching
- Fragment caching for expensive calculations
- ETags for conditional requests

### Frontend Performance
- Lazy loading for charts
- Debounced refresh requests
- Efficient DOM updates

## Security

### Authorization
- Pundit policies for role-based access
- Tenant isolation for all queries
- API rate limiting

### Data Privacy
- No sensitive data in client-side code
- Encrypted data transmission
- Audit logging for access

## Testing

### Test Coverage
- Controller tests for all endpoints
- Service tests for analytics calculations
- Integration tests for user workflows
- JavaScript tests for Stimulus controllers

### Performance Testing
- Load testing for concurrent users
- Database query optimization
- Cache hit rate monitoring

## Troubleshooting

### Common Issues

#### Slow Dashboard Loading
1. Check database query performance
2. Verify cache hit rates
3. Monitor memory usage
4. Review date range scope

#### Missing Metrics
1. Verify user permissions
2. Check data availability
3. Review cache invalidation
4. Validate date range

#### Real-time Updates Not Working
1. Check JavaScript console for errors
2. Verify AJAX endpoint responses
3. Test network connectivity
4. Review Stimulus controller setup

### Monitoring

Key metrics to monitor:
- Dashboard page load times
- Cache hit/miss ratios
- Database query performance
- User engagement rates

## Future Enhancements

### Planned Features
- Interactive charts with Chart.js
- Custom dashboard widgets
- Scheduled email reports
- Advanced filtering options
- Data export automation
- Mobile-optimized views

### Integration Opportunities
- Webhook analytics
- Third-party BI tools
- Slack/Teams notifications
- API usage analytics

## Support

For technical support or feature requests:
- Create GitHub issues for bugs
- Submit feature requests via product feedback
- Contact support for urgent issues
- Review documentation for common questions
