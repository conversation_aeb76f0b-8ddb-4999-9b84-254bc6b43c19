# Stripe subscription plans configuration
# These plans should be created in your Stripe dashboard
# Price IDs should match the ones in Stripe

plans:
  basic:
    name: "Basic"
    price_monthly: 19
    price_yearly: 190
    stripe_price_id_monthly: "price_basic_monthly"
    stripe_price_id_yearly: "price_basic_yearly"
    features:
      documents_per_month: 100
      team_members: 3
      api_access: false
      priority_support: false
      custom_templates: 5
      webhook_endpoints: 1
      retention_days: 30
      
  professional:
    name: "Professional"
    price_monthly: 49
    price_yearly: 490
    stripe_price_id_monthly: "price_professional_monthly"
    stripe_price_id_yearly: "price_professional_yearly"
    features:
      documents_per_month: 1000
      team_members: 10
      api_access: true
      priority_support: false
      custom_templates: 25
      webhook_endpoints: 5
      retention_days: 90
      
  enterprise:
    name: "Enterprise"
    price_monthly: 149
    price_yearly: 1490
    stripe_price_id_monthly: "price_enterprise_monthly"
    stripe_price_id_yearly: "price_enterprise_yearly"
    features:
      documents_per_month: -1 # unlimited
      team_members: -1 # unlimited
      api_access: true
      priority_support: true
      custom_templates: -1 # unlimited
      webhook_endpoints: -1 # unlimited
      retention_days: 365
      sla: true
      dedicated_account_manager: true