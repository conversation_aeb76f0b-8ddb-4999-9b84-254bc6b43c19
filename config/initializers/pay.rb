# frozen_string_literal: true

# Configure Pay gem for subscription and payment processing
Rails.application.configure do
  config.to_prepare do
    # Business details for receipts
    Pay.business_name = "Docutiz"
    Pay.business_address = "Docutiz, Inc."
    Pay.support_email = "<EMAIL>"
    
    # Email configuration
    Pay.send_emails = true
    Pay.mailer = "PayMailer"
    
    # Default product name for charges
    Pay.default_product_name = "Docutiz"
  end
end

# Configure Stripe
Stripe.api_key = ENV["STRIPE_SECRET_KEY"]