# Name of your application. Used to uniquely configure containers.
service: docutiz

# Name of the container image.
image: kojjob/docutiz

# Deploy to these servers.
servers:
  web:
    - ***********
  # Uncomment to run jobs on separate servers
  # job:
  #   hosts:
  #     - ***********
  #   cmd: bin/jobs

# Enable SSL auto certification via Let's Encrypt and allow for multiple apps on a single web server.
# Remove this section when using multiple web servers and ensure you terminate SSL at your load balancer.
#
# Note: If using Cloudflare, set encryption mode in SSL/TLS setting to "Full" to enable CF-to-app encryption.
proxy:
  ssl: true
  host: app.example.com

# Credentials for your image host.
registry:
  # Using Docker Hub by default. For GitHub Container Registry:
  # server: ghcr.io
  username: kojjob
  
  # Always use an access token rather than real password
  password:
    - KAMAL_REGISTRY_PASSWORD

# Inject ENV variables into containers
env:
  secret:
    - RAILS_MASTER_KEY
    - DATABASE_URL
    - OPENAI_API_KEY
    - ANTHROPIC_API_KEY
    - STRIPE_SECRET_KEY
    - STRIPE_PUBLISHABLE_KEY
    - STRIPE_WEBHOOK_SECRET
    - AWS_ACCESS_KEY_ID
    - AWS_SECRET_ACCESS_KEY
    - AWS_BUCKET
    - AWS_REGION
    - HONEYBADGER_API_KEY
  clear:
    # Rails environment
    RAILS_ENV: production
    RAILS_SERVE_STATIC_FILES: true
    RAILS_LOG_TO_STDOUT: true
    
    # Solid Queue configuration
    SOLID_QUEUE_IN_PUMA: false  # We're using separate job servers
    
    # Job processing
    JOB_CONCURRENCY: 5
    
    # Web server
    WEB_CONCURRENCY: 2
    RAILS_MAX_THREADS: 5
    
    # Application settings
    APP_DOMAIN: docutiz.com
    SUPPORT_EMAIL: <EMAIL>
    
    # Database connection (if using external DB)
    # DB_HOST: your-db-host.com
    # Or use local accessory:
    DB_HOST: docutiz-db
    
    # S3 settings
    AWS_REGION: us-east-1

# Aliases for common commands
aliases:
  console: app exec --interactive --reuse "bin/rails console"
  shell: app exec --interactive --reuse "bash"
  logs: app logs -f
  dbc: app exec --interactive --reuse "bin/rails dbconsole"
  migrate: app exec --reuse "bin/rails db:migrate"
  seed: app exec --reuse "bin/rails db:seed"

# Persistent storage volumes
volumes:
  - "docutiz_storage:/rails/storage"
  - "docutiz_uploads:/rails/public/uploads"

# Asset bridging for zero-downtime deployments
asset_path: /rails/public/assets

# Configure the image builder
builder:
  arch: amd64
  
  # Build arguments
  args:
    RUBY_VERSION: 3.3.0
  
  # Use buildkit for faster builds
  cache:
    type: registry
    options: mode=max
  
  # Multi-stage build optimizations
  dockerfile: Dockerfile.production

# SSH configuration (if not using root)
# ssh:
#   user: deploy
#   port: 2222

# Healthcheck configuration
healthcheck:
  path: /up
  port: 3000
  interval: 10s

# Accessory services
accessories:
  db:
    image: postgres:16
    host: ***********  # Same server or dedicated DB server
    port: 5432
    env:
      clear:
        POSTGRES_DB: docutiz_production
      secret:
        - POSTGRES_PASSWORD
    files:
      - db/init.sql:/docker-entrypoint-initdb.d/setup.sql
    directories:
      - data:/var/lib/postgresql/data
    options:
      restart: unless-stopped
      health-cmd: "pg_isready -U postgres"
      health-interval: 10s
      health-retries: 5

# Traefik configuration for advanced routing (optional)
# traefik:
#   options:
#     publish:
#       - "443:443"
#     volume:
#       - "/letsencrypt/acme.json:/letsencrypt/acme.json"
#   args:
#     accesslog: true
#     entryPoints.web.address: ":80"
#     entryPoints.websecure.address: ":443"
#     certificatesResolvers.letsencrypt.acme.email: "<EMAIL>"
#     certificatesResolvers.letsencrypt.acme.storage: "/letsencrypt/acme.json"
#     certificatesResolvers.letsencrypt.acme.httpChallenge.entryPoint: "web"

# Hooks for deployment lifecycle
# Place these scripts in .kamal/hooks/
# - pre-connect
# - pre-build 
# - pre-deploy
# - post-deploy