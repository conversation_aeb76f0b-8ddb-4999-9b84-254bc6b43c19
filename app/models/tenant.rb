class Tenant < ApplicationRecord
  # Include Pay gem functionality
  pay_customer default_payment_processor: :stripe
  
  # Associations
  has_many :users, dependent: :destroy
  has_many :extraction_templates, dependent: :destroy
  has_many :documents, dependent: :destroy
  has_many :team_invitations, dependent: :destroy
  has_many :activities, dependent: :destroy
  has_many :webhooks, dependent: :destroy

  # Validations
  validates :name, presence: true
  validates :subdomain, presence: true, uniqueness: true,
            format: { with: /\A[a-z0-9]+\z/, message: "only lowercase letters and numbers allowed" }

  # Callbacks
  before_validation :normalize_subdomain
  before_validation :generate_subdomain, on: :create
  after_create :create_default_admin

  # Default settings
  after_initialize do
    self.settings ||= {
      max_users: 3,
      max_documents_per_month: 1000,
      features: [ "basic_extraction" ]
    }
  end

  # Scopes
  scope :active, -> { where("trial_ends_at > ? OR plan != ?", Time.current, "trial") }
  scope :trial, -> { where(plan: "trial") }

  # Instance methods
  def trial?
    plan == "trial" && trial_ends_at.present? && trial_ends_at > Time.current
  end

  def trial_days_remaining
    return 0 unless trial?
    ((trial_ends_at - Time.current) / 1.day).ceil
  end

  def can_add_user?
    users.count < (settings["max_users"] || 3)
  end

  def documents_this_month
    # Will be implemented when we have Document model
    0
  end

  def at_document_limit?
    documents_this_month >= (settings["max_documents_per_month"] || 1000)
  end
  
  # Subscription methods
  def subscribed?
    payment_processor&.subscribed?
  end
  
  def subscription
    payment_processor&.subscription
  end
  
  def on_trial?
    subscription&.on_trial? || trial?
  end
  
  def canceled?
    subscription&.canceled?
  end
  
  def plan_name
    return "Trial" if trial? && !subscribed?
    subscription&.processor_plan || "Free"
  end
  
  def stripe_customer_id
    payment_processor&.processor_id
  end
  
  # Create or update Stripe subscription
  def subscribe_to_plan(plan_id, payment_method_id = nil)
    # Ensure we have a payment processor
    set_payment_processor(:stripe)
    
    # Update payment method if provided
    if payment_method_id
      payment_processor.update_payment_method(payment_method_id)
    end
    
    # Subscribe to the plan
    payment_processor.subscribe(plan: plan_id)
  end
  
  # Update subscription plan
  def change_plan(new_plan_id)
    return false unless subscribed?
    subscription.swap(new_plan_id)
  end
  
  # Cancel subscription
  def cancel_subscription(at_period_end: true)
    return false unless subscribed?
    
    if at_period_end
      subscription.cancel
    else
      subscription.cancel_now!
    end
  end
  
  # Resume canceled subscription
  def resume_subscription
    return false unless canceled?
    subscription.resume
  end
  
  # Check feature access based on plan
  def has_feature?(feature_name)
    return true if trial?
    
    plan_config = YAML.load_file(Rails.root.join("config/stripe/plans.yml"))["plans"]
    plan_entry =
      if subscription&.processor_plan.present?
        plan_config.find { |_key, config| config["stripe_price_id"] == subscription.processor_plan }&.last
      end
    plan_features = plan_entry&.fetch("features", {}) || plan_config.dig("basic", "features") || {}
    
    case feature_name.to_s
    when "api_access"
      plan_features["api_access"] == true
    when "priority_support"
      plan_features["priority_support"] == true
    when "unlimited_documents"
      plan_features["documents_per_month"] == -1
    when "unlimited_team_members"
      plan_features["team_members"] == -1
    else
      true # Allow by default
    end
  end
  
  # Get plan limits
  def plan_limit(limit_name)
    plan_config = YAML.load_file(Rails.root.join("config/stripe/plans.yml"))["plans"]
    plan_entry =
      if subscription&.processor_plan.present?
        plan_config.find { |_key, config| config["stripe_price_id"] == subscription.processor_plan }&.last
      end
    plan_features = plan_entry&.fetch("features", {}) || plan_config.dig("basic", "features") || {}
    
    case limit_name.to_s
    when "documents_per_month"
      plan_features["documents_per_month"] || 100
    when "team_members"
      plan_features["team_members"] || 3
    when "custom_templates"
      plan_features["custom_templates"] || 5
    when "webhook_endpoints"
      plan_features["webhook_endpoints"] || 1
    else
      nil
    end
  end
  
  # Check if at plan limit
  def at_plan_limit?(limit_name)
    limit = plan_limit(limit_name)
    return false if limit == -1 # Unlimited
    
    case limit_name.to_s
    when "documents_per_month"
      documents_this_month >= limit
    when "team_members"
      users.count >= limit
    when "custom_templates"
      extraction_templates.count >= limit
    when "webhook_endpoints"
      webhooks.active.count >= limit
    else
      false
    end
  end

  private

  def normalize_subdomain
    return unless subdomain.present?
    self.subdomain = subdomain.downcase.gsub(/[^a-z0-9]/, '')
  end

  def generate_subdomain
    return if subdomain.present?

    base = name.to_s.parameterize
    self.subdomain = base

    # Ensure uniqueness
    counter = 1
    while Tenant.exists?(subdomain: subdomain)
      self.subdomain = "#{base}#{counter}"
      counter += 1
    end
  end

  def create_default_admin
    # Created by the registration process
  end
end
