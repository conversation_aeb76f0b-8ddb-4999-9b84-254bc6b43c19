# Dashboard snapshots for storing aggregated metrics summaries
class DashboardSnapshot < ApplicationRecord
  belongs_to :tenant
  
  # Snapshot types
  SNAPSHOT_TYPES = %w[daily weekly monthly quarterly yearly].freeze
  
  validates :snapshot_type, presence: true, inclusion: { in: SNAPSHOT_TYPES }
  validates :snapshot_date, presence: true
  validates :metrics_data, presence: true
  
  # Scopes
  scope :for_type, ->(type) { where(snapshot_type: type) }
  scope :for_date_range, ->(start_date, end_date) { where(snapshot_date: start_date..end_date) }
  scope :recent, ->(limit = 12) { order(snapshot_date: :desc).limit(limit) }
  scope :daily_snapshots, -> { where(snapshot_type: 'daily') }
  scope :weekly_snapshots, -> { where(snapshot_type: 'weekly') }
  scope :monthly_snapshots, -> { where(snapshot_type: 'monthly') }
  
  # Class methods for creating snapshots
  def self.create_daily_snapshot(tenant, date = Date.current)
    metrics_data = calculate_daily_metrics(tenant, date)
    
    create_or_update_snapshot(
      tenant: tenant,
      snapshot_type: 'daily',
      snapshot_date: date,
      metrics_data: metrics_data
    )
  end
  
  def self.create_weekly_snapshot(tenant, week_start = Date.current.beginning_of_week)
    week_end = week_start.end_of_week
    metrics_data = calculate_weekly_metrics(tenant, week_start, week_end)
    
    create_or_update_snapshot(
      tenant: tenant,
      snapshot_type: 'weekly',
      snapshot_date: week_start,
      metrics_data: metrics_data
    )
  end
  
  def self.create_monthly_snapshot(tenant, month_start = Date.current.beginning_of_month)
    month_end = month_start.end_of_month
    metrics_data = calculate_monthly_metrics(tenant, month_start, month_end)
    
    create_or_update_snapshot(
      tenant: tenant,
      snapshot_type: 'monthly',
      snapshot_date: month_start,
      metrics_data: metrics_data
    )
  end
  
  # Instance methods
  def total_documents
    metrics_data.dig('overview', 'total_documents') || 0
  end
  
  def success_rate
    metrics_data.dig('overview', 'success_rate') || 0
  end
  
  def active_users
    metrics_data.dig('overview', 'active_users') || 0
  end
  
  def revenue
    metrics_data.dig('billing', 'revenue') || 0
  end
  
  def processing_time
    metrics_data.dig('overview', 'avg_processing_time') || 0
  end
  
  def growth_rate(metric_key)
    previous_snapshot = self.class
                           .where(tenant: tenant, snapshot_type: snapshot_type)
                           .where('snapshot_date < ?', snapshot_date)
                           .order(snapshot_date: :desc)
                           .first
    
    return 0 unless previous_snapshot
    
    current_value = metrics_data.dig(*metric_key.split('.')) || 0
    previous_value = previous_snapshot.metrics_data.dig(*metric_key.split('.')) || 0
    
    return 0 if previous_value.zero?
    
    ((current_value - previous_value) / previous_value.to_f * 100).round(2)
  end
  
  private
  
  def self.create_or_update_snapshot(tenant:, snapshot_type:, snapshot_date:, metrics_data:)
    snapshot = find_or_initialize_by(
      tenant: tenant,
      snapshot_type: snapshot_type,
      snapshot_date: snapshot_date
    )
    
    snapshot.metrics_data = metrics_data
    snapshot.save!
    snapshot
  end
  
  def self.calculate_daily_metrics(tenant, date)
    start_time = date.beginning_of_day
    end_time = date.end_of_day
    
    {
      overview: {
        total_documents: tenant.documents.where(created_at: start_time..end_time).count,
        success_rate: calculate_success_rate(tenant, start_time, end_time),
        active_users: tenant.users.where(last_sign_in_at: start_time..end_time).count,
        avg_processing_time: calculate_avg_processing_time(tenant, start_time, end_time)
      },
      document_processing: {
        processed: tenant.documents.where(status: 'completed', updated_at: start_time..end_time).count,
        failed: tenant.documents.where(status: 'failed', updated_at: start_time..end_time).count,
        pending: tenant.documents.where(status: 'pending', created_at: start_time..end_time).count
      },
      user_activity: {
        new_users: tenant.users.where(created_at: start_time..end_time).count,
        active_users: tenant.users.where(last_sign_in_at: start_time..end_time).count,
        documents_uploaded: tenant.documents.where(created_at: start_time..end_time).count
      }
    }
  end
  
  def self.calculate_weekly_metrics(tenant, start_date, end_date)
    start_time = start_date.beginning_of_day
    end_time = end_date.end_of_day
    
    calculate_daily_metrics(tenant, start_date).merge(
      timeframe: 'weekly',
      start_date: start_date,
      end_date: end_date
    )
  end
  
  def self.calculate_monthly_metrics(tenant, start_date, end_date)
    start_time = start_date.beginning_of_day
    end_time = end_date.end_of_day
    
    calculate_daily_metrics(tenant, start_date).merge(
      timeframe: 'monthly',
      start_date: start_date,
      end_date: end_date
    )
  end
  
  def self.calculate_success_rate(tenant, start_time, end_time)
    total = tenant.documents.where(created_at: start_time..end_time).count
    return 0 if total.zero?
    
    successful = tenant.documents.where(status: 'completed', created_at: start_time..end_time).count
    (successful.to_f / total * 100).round(2)
  end
  
  def self.calculate_avg_processing_time(tenant, start_time, end_time)
    completed_docs = tenant.documents
                           .where(status: 'completed', updated_at: start_time..end_time)
                           .where.not(processing_started_at: nil)
    
    return 0 if completed_docs.empty?
    
    total_time = completed_docs.sum do |doc|
      (doc.updated_at - doc.processing_started_at).to_f
    end
    
    (total_time / completed_docs.count).round(2)
  end
end
