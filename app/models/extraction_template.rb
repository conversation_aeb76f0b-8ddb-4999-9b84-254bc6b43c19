class ExtractionTemplate < ApplicationRecord
  # Associations
  belongs_to :tenant
  has_many :documents, dependent: :nullify
  has_many :extraction_results, through: :documents

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :document_type, presence: true
  validates :fields, presence: true
  validates :prompt_template, presence: true

  # Scopes
  scope :active, -> { where(active: true) }
  scope :by_document_type, ->(type) { where(document_type: type) }

  # Callbacks
  after_create_commit :trigger_created_webhook
  after_update_commit :trigger_updated_webhook

  # Document types
  DOCUMENT_TYPES = %w[
    invoice
    receipt
    bank_statement
    contract
    form
    id_document
    other
  ].freeze

  validates :document_type, inclusion: { in: DOCUMENT_TYPES }

  # Default settings
  after_initialize do
    self.settings ||= {
      "confidence_threshold" => 0.8,
      "require_human_review" => false,
      "auto_approve" => true
    }
    self.fields ||= []
    self.active = true if active.nil?
  end

  # Instance methods
  def field_names
    fields.map { |f| f["name"] }
  end

  def required_fields
    fields.select { |f| f["required"] == true }
  end

  def optional_fields
    fields.reject { |f| f["required"] == true }
  end

  def generate_prompt(document_context = {})
    # Replace template variables with actual values
    prompt = prompt_template.dup
    
    document_context.each do |key, value|
      prompt.gsub!("{{#{key}}}", value.to_s)
    end
    
    # Replace fields_list with formatted field descriptions
    if prompt.include?("{{fields_list}}")
      fields_description = fields.map do |field|
        "- #{field['name']}: #{field['description']}"
      end.join("\n")
      prompt.gsub!("{{fields_list}}", fields_description)
    end
    
    prompt
  end
  
  def average_confidence_score
    return 0.0 if extraction_results.empty?
    
    extraction_results.average(:confidence_score) || 0.0
  end
  
  def extraction_success_rate
    return 0.0 if extraction_results.empty?
    
    successful = extraction_results.where('confidence_score >= ?', 0.8).count
    total = extraction_results.count
    
    (successful.to_f / total * 100).round(1)
  end
  
  # Generate JSON Schema from template fields
  def schema
    {
      "$schema" => "https://json-schema.org/draft/2020-12/schema",
      "title" => name,
      "description" => "Schema for #{document_type} extraction",
      "type" => "object",
      "properties" => fields.each_with_object({}) do |field, props|
        props[field["name"]] = build_field_schema(field)
      end,
      "required" => required_fields.map { |f| f["name"] }
    }
  end
  
  # Export template as a shareable configuration
  def export
    {
      name: name,
      document_type: document_type,
      description: description,
      fields: fields,
      prompt_template: prompt_template,
      settings: settings,
      version: "1.0",
      exported_at: Time.current.iso8601
    }
  end
  
  # Import template configuration
  def self.import(tenant, config)
    template = tenant.extraction_templates.build(
      name: config["name"] || config[:name],
      document_type: config["document_type"] || config[:document_type],
      description: config["description"] || config[:description],
      fields: config["fields"] || config[:fields],
      prompt_template: config["prompt_template"] || config[:prompt_template],
      settings: config["settings"] || config[:settings] || {}
    )
    
    # Ensure unique name within tenant
    if tenant.extraction_templates.exists?(name: template.name)
      template.name = "#{template.name} (imported #{Time.current.strftime('%Y-%m-%d')})"
    end
    
    template.save!
    template
  end

  private
  
  def build_field_schema(field)
    schema = {
      "description" => field["description"]
    }
    
    case field["type"]
    when "string", "text"
      schema["type"] = "string"
      schema["minLength"] = field["min_length"] if field["min_length"]
      schema["maxLength"] = field["max_length"] if field["max_length"]
      schema["pattern"] = field["pattern"] if field["pattern"]
      schema["enum"] = field["allowed_values"] if field["allowed_values"]
    when "number", "decimal", "float"
      schema["type"] = "number"
      schema["minimum"] = field["min_value"] if field["min_value"]
      schema["maximum"] = field["max_value"] if field["max_value"]
    when "integer"
      schema["type"] = "integer"
      schema["minimum"] = field["min_value"] if field["min_value"]
      schema["maximum"] = field["max_value"] if field["max_value"]
    when "boolean"
      schema["type"] = "boolean"
    when "date"
      schema["type"] = "string"
      schema["format"] = "date"
    when "datetime"
      schema["type"] = "string"
      schema["format"] = "date-time"
    when "array"
      schema["type"] = "array"
      schema["items"] = { "type" => field["item_type"] || "string" }
      schema["minItems"] = field["min_items"] if field["min_items"]
      schema["maxItems"] = field["max_items"] if field["max_items"]
    when "object"
      schema["type"] = "object"
      schema["properties"] = field["properties"] || {}
    else
      schema["type"] = "string" # Default to string
    end
    
    schema
  end

  def trigger_created_webhook
    trigger_webhook("template.created")
  end

  def trigger_updated_webhook
    trigger_webhook("template.updated")
  end

  def trigger_webhook(event)
    payload = {
      event: event,
      timestamp: Time.current.iso8601,
      template: {
        id: id,
        name: name,
        document_type: document_type,
        active: active,
        fields_count: fields.size,
        settings: settings,
        created_at: created_at.iso8601,
        updated_at: updated_at.iso8601
      },
      tenant: {
        id: tenant.id,
        name: tenant.name,
        subdomain: tenant.subdomain
      }
    }

    # Find all active webhooks for this event
    tenant.webhooks.for_event(event).each do |webhook|
      webhook.trigger(event, payload)
    end
  end
end
