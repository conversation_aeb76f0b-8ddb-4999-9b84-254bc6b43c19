# Dashboard metrics model for storing historical analytics data
class DashboardMetric < ApplicationRecord
  belongs_to :tenant
  
  # Metric types
  METRIC_TYPES = %w[
    document_count
    success_rate
    processing_time
    user_activity
    revenue
    api_calls
    error_rate
    queue_length
    storage_usage
  ].freeze
  
  # Time periods
  TIME_PERIODS = %w[hourly daily weekly monthly].freeze
  
  validates :metric_type, presence: true, inclusion: { in: METRIC_TYPES }
  validates :time_period, presence: true, inclusion: { in: TIME_PERIODS }
  validates :recorded_at, presence: true
  validates :value, presence: true, numericality: true
  
  # Scopes for querying metrics
  scope :for_type, ->(type) { where(metric_type: type) }
  scope :for_period, ->(period) { where(time_period: period) }
  scope :for_date_range, ->(start_date, end_date) { where(recorded_at: start_date..end_date) }
  scope :recent, ->(limit = 30) { order(recorded_at: :desc).limit(limit) }
  scope :daily_metrics, -> { where(time_period: 'daily') }
  scope :hourly_metrics, -> { where(time_period: 'hourly') }
  
  # Class methods for analytics
  def self.record_metric(tenant, metric_type, value, time_period: 'daily', recorded_at: Time.current, metadata: {})
    create!(
      tenant: tenant,
      metric_type: metric_type,
      value: value,
      time_period: time_period,
      recorded_at: recorded_at,
      metadata: metadata
    )
  end
  
  def self.average_for_period(metric_type, start_date, end_date)
    for_type(metric_type)
      .for_date_range(start_date, end_date)
      .average(:value) || 0
  end
  
  def self.sum_for_period(metric_type, start_date, end_date)
    for_type(metric_type)
      .for_date_range(start_date, end_date)
      .sum(:value) || 0
  end
  
  def self.trend_data(metric_type, days: 30)
    end_date = Date.current
    start_date = end_date - days.days
    
    for_type(metric_type)
      .daily_metrics
      .for_date_range(start_date, end_date)
      .order(:recorded_at)
      .pluck(:recorded_at, :value)
      .map { |date, value| { date: date.strftime('%Y-%m-%d'), value: value } }
  end
  
  def self.latest_value(metric_type)
    for_type(metric_type)
      .order(recorded_at: :desc)
      .first&.value || 0
  end
  
  # Instance methods
  def percentage?
    %w[success_rate error_rate].include?(metric_type)
  end
  
  def currency?
    %w[revenue].include?(metric_type)
  end
  
  def time_based?
    %w[processing_time].include?(metric_type)
  end
  
  def formatted_value
    if percentage?
      "#{value.round(1)}%"
    elsif currency?
      "$#{value.round(2)}"
    elsif time_based?
      "#{value.round(2)}s"
    else
      value.to_i.to_s
    end
  end
end
