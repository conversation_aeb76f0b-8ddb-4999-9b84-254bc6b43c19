<!-- Minimal footer for dashboard/tenant pages -->
<footer class="bg-white border-t border-gray-200 mt-auto">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
      <!-- Left Side - Company Info -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div class="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span class="font-semibold text-gray-900">Docutiz</span>
        </div>
        
        <div class="hidden md:flex items-center space-x-4 text-sm text-gray-500">
          <span>&copy; <%= Date.current.year %></span>
          <span class="text-gray-300">|</span>
          <a href="/status" class="flex items-center space-x-1 hover:text-gray-700 transition-colors">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>System Status</span>
          </a>
        </div>
      </div>
      
      <!-- Right Side - Quick Links -->
      <div class="flex items-center space-x-6 text-sm">
        <a href="/help" class="text-gray-500 hover:text-gray-700 transition-colors">
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Help Center
        </a>
        
        <a href="/api-docs" class="text-gray-500 hover:text-gray-700 transition-colors">
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
          </svg>
          API Docs
        </a>
        
        <a href="mailto:<EMAIL>" class="text-gray-500 hover:text-gray-700 transition-colors">
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          Support
        </a>
        
        <!-- Version Info -->
        <div class="hidden lg:flex items-center text-xs text-gray-400">
          <span>v2.1.0</span>
        </div>
      </div>
    </div>
    
    <!-- Mobile Version Info -->
    <div class="md:hidden flex justify-center items-center space-x-4 text-xs text-gray-400 mt-4 pt-4 border-t border-gray-100">
      <span>&copy; <%= Date.current.year %> Docutiz</span>
      <span class="text-gray-300">|</span>
      <span>v2.1.0</span>
      <span class="text-gray-300">|</span>
      <a href="/status" class="flex items-center space-x-1">
        <div class="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
        <span>Operational</span>
      </a>
    </div>
  </div>
</footer>