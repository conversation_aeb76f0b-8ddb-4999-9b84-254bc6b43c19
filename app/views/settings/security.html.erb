<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Settings</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage your account settings and preferences
      </p>
    </div>

    <!-- Settings Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-8 border-b border-gray-200 dark:border-coffee-700">
        <%= link_to profile_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(profile_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Profile
        <% end %>
        
        <%= link_to notifications_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(notifications_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Notifications
        <% end %>
        
        <%= link_to collaboration_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(collaboration_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Collaboration
        <% end %>
        
        <%= link_to api_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(api_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          API
        <% end %>
        
        <%= link_to integrations_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(integrations_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Integrations
        <% end %>
        
        <% if current_user.owner? %>
          <%= link_to tenant_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(tenant_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Organization
          <% end %>
          
          <%= link_to billing_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(billing_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Billing
          <% end %>
          
          <%= link_to security_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(security_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Security
          <% end %>
        <% end %>
      </nav>
    </div>

    <!-- Security Settings Form -->
    <%= form_with url: security_settings_path, method: :patch, local: true, class: "space-y-6" do |f| %>
      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Authentication</h3>
        </div>
        <div class="px-6 py-6 space-y-6">
          <div>
            <label class="flex items-start">
              <%= check_box_tag "security[require_2fa]", 
                  true, 
                  @security_settings["require_2fa"],
                  class: "h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded mt-1 mr-3" %>
              <div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Require Two-Factor Authentication</span>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  All team members must enable 2FA to access the organization
                </p>
              </div>
            </label>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Session Timeout (minutes)
            </label>
            <%= number_field_tag "security[session_timeout]", 
                @security_settings["session_timeout"],
                min: 15,
                max: 10080,
                step: 15,
                class: "block w-full rounded-lg border-gray-300 dark:border-coffee-700 dark:bg-coffee-900 dark:text-gray-100 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm" %>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Automatically sign out users after this period of inactivity (15 minutes to 1 week)
            </p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Password Expiry (days)
            </label>
            <%= number_field_tag "security[password_expiry_days]", 
                @security_settings["password_expiry_days"],
                min: 0,
                max: 365,
                step: 30,
                class: "block w-full rounded-lg border-gray-300 dark:border-coffee-700 dark:bg-coffee-900 dark:text-gray-100 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm" %>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Force users to change passwords after this many days (0 to disable)
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Access Control</h3>
        </div>
        <div class="px-6 py-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              IP Whitelist
            </label>
            <%= text_area_tag "security[ip_whitelist]", 
                @security_settings["ip_whitelist"],
                rows: 4,
                placeholder: "Enter IP addresses or ranges, one per line\nExample:\n192.168.1.0/24\n10.0.0.1",
                class: "block w-full rounded-lg border-gray-300 dark:border-coffee-700 dark:bg-coffee-900 dark:text-gray-100 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm font-mono" %>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Leave empty to allow access from any IP address
            </p>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-end">
        <%= f.submit "Update Security Settings", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 cursor-pointer" %>
      </div>
    <% end %>

    <!-- Recent Security Activity -->
    <div class="mt-10 bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Recent Security Activity</h3>
      </div>
      <div class="px-6 py-4">
        <div class="flow-root">
          <ul class="-mb-8">
            <% @recent_activities.each_with_index do |activity, index| %>
              <li>
                <div class="relative pb-8">
                  <% unless index == @recent_activities.size - 1 %>
                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-coffee-700" aria-hidden="true"></span>
                  <% end %>
                  <div class="relative flex space-x-3">
                    <div>
                      <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-coffee-800
                        <%= case activity.action
                            when 'user_login' then 'bg-green-500'
                            when 'user_logout' then 'bg-gray-400'
                            when 'password_changed' then 'bg-yellow-500'
                            when 'api_token_regenerated' then 'bg-blue-500'
                            else 'bg-gray-400'
                            end %>">
                        <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <% case activity.action %>
                          <% when 'user_login' %>
                            <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                          <% when 'user_logout' %>
                            <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd" />
                          <% when 'password_changed' %>
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                          <% when 'api_token_regenerated' %>
                            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                          <% end %>
                        </svg>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-900 dark:text-gray-100">
                          <%= activity.user.name %>
                          <span class="font-medium">
                            <%= case activity.action
                                when 'user_login' then 'signed in'
                                when 'user_logout' then 'signed out'
                                when 'password_changed' then 'changed password'
                                when 'api_token_regenerated' then 'regenerated API token'
                                end %>
                          </span>
                        </p>
                        <% if activity.metadata['ip_address'] %>
                          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            IP: <%= activity.metadata['ip_address'] %>
                          </p>
                        <% end %>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <%= time_ago_in_words(activity.created_at) %> ago
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>