<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Settings</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage your account settings and preferences
      </p>
    </div>

    <!-- Settings Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-8 border-b border-gray-200 dark:border-coffee-700">
        <%= link_to profile_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(profile_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Profile
        <% end %>
        
        <%= link_to notifications_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(notifications_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Notifications
        <% end %>
        
        <%= link_to collaboration_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(collaboration_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Collaboration
        <% end %>
        
        <%= link_to api_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(api_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          API
        <% end %>
        
        <%= link_to integrations_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(integrations_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Integrations
        <% end %>
        
        <% if current_user.owner? %>
          <%= link_to tenant_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(tenant_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Organization
          <% end %>
          
          <%= link_to billing_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(billing_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Billing
          <% end %>
          
          <%= link_to security_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(security_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Security
          <% end %>
        <% end %>
      </nav>
    </div>

    <!-- Current Plan -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 mb-6">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Current Plan</h3>
      </div>
      <div class="px-6 py-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h4 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              <%= @subscription[:plan]&.capitalize || "Free Trial" %>
            </h4>
            <% if @subscription[:trial_ends_at] && @subscription[:trial_ends_at] > Time.current %>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Trial ends <%= @subscription[:trial_ends_at].strftime("%B %d, %Y") %>
              </p>
            <% end %>
          </div>
          <div class="text-right">
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              <%= @subscription[:plan] == "enterprise" ? "Custom" : "$49" %>
              <span class="text-base font-normal text-gray-500 dark:text-gray-400">/month</span>
            </p>
          </div>
        </div>

        <div class="space-y-4">
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Documents Processed</span>
              <span class="text-gray-900 dark:text-gray-100">
                <%= @subscription[:documents_used] %> / <%= @subscription[:documents_limit] || "Unlimited" %>
              </span>
            </div>
            <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div class="bg-teal-600 h-2 rounded-full" style="width: <%= (@subscription[:documents_used].to_f / (@subscription[:documents_limit] || 1000) * 100).clamp(0, 100) %>%"></div>
            </div>
          </div>
        </div>

        <div class="mt-6">
          <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
            Upgrade Plan
          </button>
        </div>
      </div>
    </div>

    <!-- Available Plans -->
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Available Plans</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <!-- Starter Plan -->
        <div class="relative bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 p-6">
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Starter</h3>
            <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-gray-100">
              $29
              <span class="text-base font-normal text-gray-500 dark:text-gray-400">/month</span>
            </p>
          </div>
          <ul class="space-y-3 mb-6">
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">100 documents/month</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">5 extraction templates</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">2 team members</span>
            </li>
          </ul>
        </div>

        <!-- Professional Plan -->
        <div class="relative bg-white dark:bg-coffee-800 shadow-sm rounded-lg border-2 border-teal-500 p-6">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="inline-flex px-3 py-1 text-xs font-semibold text-teal-800 bg-teal-100 rounded-full">
              Most Popular
            </span>
          </div>
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Professional</h3>
            <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-gray-100">
              $49
              <span class="text-base font-normal text-gray-500 dark:text-gray-400">/month</span>
            </p>
          </div>
          <ul class="space-y-3 mb-6">
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">500 documents/month</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Unlimited templates</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">10 team members</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">API access</span>
            </li>
          </ul>
        </div>

        <!-- Enterprise Plan -->
        <div class="relative bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 p-6">
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Enterprise</h3>
            <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-gray-100">
              Custom
            </p>
          </div>
          <ul class="space-y-3 mb-6">
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Unlimited documents</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Unlimited templates</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Unlimited team members</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Priority support</span>
            </li>
            <li class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">SLA guarantee</span>
            </li>
          </ul>
          <button type="button" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-coffee-700 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-200 bg-white dark:bg-coffee-800 hover:bg-gray-50 dark:hover:bg-coffee-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
            Contact Sales
          </button>
        </div>
      </div>
    </div>

    <!-- Payment Method -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 mb-6">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Payment Method</h3>
      </div>
      <div class="px-6 py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 3h18v18H3V3zm16 16V7H5v12h14zm-2-8h-4v2h4v-2zm-6 0H7v2h4v-2zm6-4h-4v2h4V7zm-6 0H7v2h4V7z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                •••• •••• •••• 4242
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Expires 12/24
              </p>
            </div>
          </div>
          <button type="button" class="text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400">
            Update
          </button>
        </div>
      </div>
    </div>

    <!-- Billing History -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Billing History</h3>
      </div>
      <div class="px-6 py-4">
        <div class="overflow-hidden">
          <table class="min-w-full">
            <thead>
              <tr>
                <th class="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th class="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Description
                </th>
                <th class="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th class="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Invoice
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-coffee-700">
              <tr>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  Jan 1, 2024
                </td>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  Professional Plan - Monthly
                </td>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  $49.00
                </td>
                <td class="py-4 text-sm">
                  <a href="#" class="text-teal-600 hover:text-teal-500 dark:text-teal-400">Download</a>
                </td>
              </tr>
              <tr>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  Dec 1, 2023
                </td>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  Professional Plan - Monthly
                </td>
                <td class="py-4 text-sm text-gray-900 dark:text-gray-100">
                  $49.00
                </td>
                <td class="py-4 text-sm">
                  <a href="#" class="text-teal-600 hover:text-teal-500 dark:text-teal-400">Download</a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>