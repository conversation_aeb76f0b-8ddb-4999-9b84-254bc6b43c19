<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Settings</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage your account settings and preferences
      </p>
    </div>

    <!-- Settings Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-8 border-b border-gray-200 dark:border-coffee-700">
        <%= link_to profile_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(profile_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Profile
        <% end %>
        
        <%= link_to notifications_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(notifications_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Notifications
        <% end %>
        
        <%= link_to collaboration_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(collaboration_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Collaboration
        <% end %>
        
        <%= link_to api_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(api_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          API
        <% end %>
        
        <%= link_to integrations_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(integrations_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Integrations
        <% end %>
        
        <% if current_user.owner? %>
          <%= link_to tenant_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(tenant_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Organization
          <% end %>
          
          <%= link_to billing_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(billing_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Billing
          <% end %>
          
          <%= link_to security_settings_path, class: "py-2 px-1 border-b-2 font-medium text-sm #{current_page?(security_settings_path) ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
            Security
          <% end %>
        <% end %>
      </nav>
    </div>

    <!-- Organization Settings Form -->
    <%= form_with model: @tenant, url: tenant_settings_path, method: :patch, local: true, class: "space-y-6" do |f| %>
      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Organization Information</h3>
        </div>
        <div class="px-6 py-6">
          <div>
            <%= f.label :name, "Organization Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
            <%= f.text_field :name, 
                class: "block w-full rounded-lg border-gray-300 dark:border-coffee-700 dark:bg-coffee-900 dark:text-gray-100 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm" %>
            <% if @tenant.errors[:name].any? %>
              <p class="mt-1 text-sm text-red-600 dark:text-red-400"><%= @tenant.errors[:name].first %></p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Organization Statistics -->
      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Usage Statistics</h3>
        </div>
        <div class="px-6 py-6">
          <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2">
            <div class="bg-gray-50 dark:bg-coffee-900 px-4 py-5 rounded-lg">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-gray-100">
                <%= number_with_delimiter(@tenant_stats[:total_users]) %>
              </dd>
            </div>
            
            <div class="bg-gray-50 dark:bg-coffee-900 px-4 py-5 rounded-lg">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Documents</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-gray-100">
                <%= number_with_delimiter(@tenant_stats[:total_documents]) %>
              </dd>
            </div>
            
            <div class="bg-gray-50 dark:bg-coffee-900 px-4 py-5 rounded-lg">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Extraction Templates</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-gray-100">
                <%= number_with_delimiter(@tenant_stats[:total_templates]) %>
              </dd>
            </div>
            
            <div class="bg-gray-50 dark:bg-coffee-900 px-4 py-5 rounded-lg">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Storage Used</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-gray-100">
                <%= number_to_human_size(@tenant_stats[:storage_used]) %>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Organization Settings -->
      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
          <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Organization Settings</h3>
        </div>
        <div class="px-6 py-6 space-y-6">
          <div>
            <dt class="text-sm font-medium text-gray-700 dark:text-gray-300">Subdomain</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">
              <%= @tenant.subdomain %>.docutiz.com
            </dd>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              This cannot be changed after creation
            </p>
          </div>
          
          <div>
            <dt class="text-sm font-medium text-gray-700 dark:text-gray-300">Organization ID</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">
              <%= @tenant.id %>
            </dd>
          </div>
          
          <div>
            <dt class="text-sm font-medium text-gray-700 dark:text-gray-300">Created</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @tenant.created_at.strftime("%B %d, %Y at %I:%M %p") %>
            </dd>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-end">
        <%= f.submit "Update Organization", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 cursor-pointer" %>
      </div>
    <% end %>

    <!-- Danger Zone -->
    <div class="mt-10 bg-red-50 dark:bg-red-900/20 shadow-sm rounded-lg border border-red-200 dark:border-red-800">
      <div class="px-6 py-4 border-b border-red-200 dark:border-red-800">
        <h3 class="text-base font-semibold text-red-900 dark:text-red-200">Danger Zone</h3>
      </div>
      <div class="px-6 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Delete Organization</h4>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Once you delete your organization, there is no going back. All data will be permanently deleted.
            </p>
          </div>
          <button type="button" class="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-700 text-sm font-medium rounded-lg text-red-700 dark:text-red-300 bg-white dark:bg-coffee-800 hover:bg-red-50 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete Organization
          </button>
        </div>
      </div>
    </div>
  </div>
</div>