<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center sm:justify-between mb-8">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Webhooks</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Configure webhooks to receive real-time notifications about events in your account
      </p>
    </div>
    <div class="mt-4 sm:mt-0">
      <%= link_to new_webhook_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" do %>
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add Webhook
      <% end %>
    </div>
  </div>

  <% if @webhooks.any? %>
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <div class="flow-root">
          <ul class="-my-5 divide-y divide-gray-200 dark:divide-coffee-700">
            <% @webhooks.each do |webhook| %>
              <li class="py-5">
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                      <%= link_to webhook_path(webhook), class: "text-lg font-medium text-gray-900 dark:text-gray-100 hover:text-teal-600 dark:hover:text-teal-400" do %>
                        <%= webhook.name %>
                      <% end %>
                      <% if webhook.active? %>
                        <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          Active
                        </span>
                      <% else %>
                        <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                          Inactive
                        </span>
                      <% end %>
                    </div>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      <%= webhook.redacted_url %>
                    </p>
                    <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                      <span>
                        <%= pluralize(webhook.events.size, 'event') %> subscribed
                      </span>
                      <span class="text-gray-300 dark:text-gray-600">•</span>
                      <span>
                        <%= number_with_delimiter(webhook.total_deliveries) %> deliveries
                      </span>
                      <% if webhook.total_deliveries > 0 %>
                        <span class="text-gray-300 dark:text-gray-600">•</span>
                        <span class="<%= webhook.success_rate >= 90 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' %>">
                          <%= webhook.success_rate %>% success rate
                        </span>
                      <% end %>
                    </div>
                  </div>
                  <div class="ml-4 flex items-center space-x-2">
                    <%= link_to test_webhook_path(webhook), method: :post, data: { confirm: "Send a test webhook?" }, class: "p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" do %>
                      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    <% end %>
                    <%= link_to edit_webhook_path(webhook), class: "p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" do %>
                      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    <% end %>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  <% else %>
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-12 sm:p-16 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No webhooks</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Get started by creating your first webhook endpoint.
        </p>
        <div class="mt-6">
          <%= link_to new_webhook_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" do %>
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Webhook
          <% end %>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Webhook Documentation -->
  <div class="mt-8 bg-gray-50 dark:bg-coffee-900/50 rounded-lg p-6">
    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Quick Start</h3>
    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
      Webhooks allow you to receive HTTP POST notifications when specific events occur in your Docutiz account.
    </p>
    <div class="space-y-2 text-sm">
      <div class="flex items-start">
        <span class="text-gray-500 dark:text-gray-400 mr-2">1.</span>
        <span class="text-gray-700 dark:text-gray-300">Create a webhook endpoint on your server that can receive POST requests</span>
      </div>
      <div class="flex items-start">
        <span class="text-gray-500 dark:text-gray-400 mr-2">2.</span>
        <span class="text-gray-700 dark:text-gray-300">Add the endpoint URL and select the events you want to subscribe to</span>
      </div>
      <div class="flex items-start">
        <span class="text-gray-500 dark:text-gray-400 mr-2">3.</span>
        <span class="text-gray-700 dark:text-gray-300">Verify webhook signatures using the secret key to ensure authenticity</span>
      </div>
    </div>
    <div class="mt-4">
      <a href="/api-docs#webhooks" class="text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400">
        View webhook documentation →
      </a>
    </div>
  </div>
</div>