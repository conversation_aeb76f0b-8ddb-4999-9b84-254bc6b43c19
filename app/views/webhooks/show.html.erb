<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center sm:justify-between mb-8">
    <div>
      <div class="flex items-center">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100"><%= @webhook.name %></h1>
        <% if @webhook.active? %>
          <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
            Active
          </span>
        <% else %>
          <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
            Inactive
          </span>
        <% end %>
      </div>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        <%= @webhook.url %>
      </p>
    </div>
    <div class="mt-4 sm:mt-0 flex items-center space-x-3">
      <%= link_to test_webhook_path(@webhook), method: :post, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-coffee-800 hover:bg-gray-50 dark:hover:bg-coffee-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" do %>
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Test Webhook
      <% end %>
      <%= link_to edit_webhook_path(@webhook), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" do %>
        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        Edit
      <% end %>
    </div>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5 mb-8">
    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
          Total Events
        </dt>
        <dd class="mt-1 text-3xl font-semibold text-gray-900 dark:text-gray-100">
          <%= number_with_delimiter(@stats[:total_events]) %>
        </dd>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
          Delivered
        </dt>
        <dd class="mt-1 text-3xl font-semibold text-green-600 dark:text-green-400">
          <%= number_with_delimiter(@stats[:delivered]) %>
        </dd>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
          Failed
        </dt>
        <dd class="mt-1 text-3xl font-semibold text-red-600 dark:text-red-400">
          <%= number_with_delimiter(@stats[:failed]) %>
        </dd>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
          Pending
        </dt>
        <dd class="mt-1 text-3xl font-semibold text-yellow-600 dark:text-yellow-400">
          <%= number_with_delimiter(@stats[:pending]) %>
        </dd>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-4 py-5 sm:p-6">
        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
          Success Rate
        </dt>
        <dd class="mt-1 text-3xl font-semibold <%= @stats[:success_rate] >= 90 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' %>">
          <%= @stats[:success_rate] %>%
        </dd>
      </div>
    </div>
  </div>

  <!-- Tabs -->
  <div class="mb-8">
    <div class="border-b border-gray-200 dark:border-coffee-700">
      <nav class="-mb-px flex space-x-8">
        <%= link_to webhook_path(@webhook), class: "py-2 px-1 border-b-2 font-medium text-sm #{!request.path.include?('/events') ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Overview
        <% end %>
        <%= link_to events_webhook_path(@webhook), class: "py-2 px-1 border-b-2 font-medium text-sm #{request.path.include?('/events') ? 'border-teal-500 text-teal-600 dark:text-teal-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'}" do %>
          Events
        <% end %>
      </nav>
    </div>
  </div>

  <!-- Overview Content -->
  <div class="space-y-6">
    <!-- Configuration -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Configuration</h3>
      </div>
      <div class="px-6 py-6">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Endpoint URL</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono break-all">
              <%= @webhook.url %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secret Key</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <div class="flex items-center">
                <code class="font-mono text-xs bg-gray-100 dark:bg-coffee-900 px-2 py-1 rounded">
                  <%= @webhook.secret_key %>
                </code>
                <button type="button" onclick="copyToClipboard('<%= @webhook.secret_key %>')" class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Retry Count</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @webhook.retry_count %> attempts
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Timeout</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @webhook.timeout_seconds %> seconds
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @webhook.created_at.strftime("%B %d, %Y at %I:%M %p") %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Triggered</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @webhook.last_triggered_at ? time_ago_in_words(@webhook.last_triggered_at) + " ago" : "Never" %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Subscribed Events -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Subscribed Events</h3>
      </div>
      <div class="px-6 py-6">
        <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
          <% @webhook.events.each do |event| %>
            <div class="flex items-center">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm text-gray-900 dark:text-gray-100">
                <%= event %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Events -->
    <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700 flex items-center justify-between">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Recent Events</h3>
        <%= link_to "View All", events_webhook_path(@webhook), class: "text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400" %>
      </div>
      <div class="px-6 py-4">
        <% if @recent_events.any? %>
          <div class="flow-root">
            <ul class="-my-5 divide-y divide-gray-200 dark:divide-coffee-700">
              <% @recent_events.each do |event| %>
                <li class="py-4">
                  <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                      <% case event.status %>
                      <% when 'delivered' %>
                        <div class="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                          <svg class="h-5 w-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      <% when 'failed' %>
                        <div class="h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                          <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      <% else %>
                        <div class="h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center">
                          <svg class="h-5 w-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      <% end %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <%= event.event_type %>
                      </p>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        <%= time_ago_in_words(event.created_at) %> ago
                        <% if event.response_code %>
                          • <%= event.response_code %>
                        <% end %>
                        <% if event.response_time %>
                          • <%= (event.response_time * 1000).round %>ms
                        <% end %>
                      </p>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
          </div>
        <% else %>
          <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
            No events yet. Send a test webhook to get started.
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(function() {
    // Show feedback
    alert('Copied to clipboard!');
  });
}
</script>