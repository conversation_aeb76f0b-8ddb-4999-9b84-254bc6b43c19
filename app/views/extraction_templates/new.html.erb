<div class="px-4 py-8 sm:px-6 lg:px-8 max-w-6xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Create Extraction Template</h1>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Define how to extract data from your documents</p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Left Column: AI Builder -->
    <div>
      <%= render 'claude_builder' %>
    </div>
    
    <!-- Right Column: Manual Form -->
    <div>
      <%= form_with(model: [@extraction_template], local: true) do |form| %>
        <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Template Details</h3>
          
          <% if @extraction_template.errors.any? %>
            <div class="mb-6 rounded-lg bg-red-50 dark:bg-red-900/20 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    There were <%= @extraction_template.errors.count %> errors with your submission
                  </h3>
                  <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <ul class="list-disc space-y-1 pl-5">
                      <% @extraction_template.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          
          <div class="space-y-6">
            <!-- Template Name -->
            <div>
              <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_field :name, 
                  class: "block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100",
                  placeholder: "e.g., Invoice Extraction Template" %>
            </div>
            
            <!-- Document Type -->
            <div>
              <%= form.label :document_type, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.select :document_type,
                  options_for_select(ExtractionTemplate::DOCUMENT_TYPES.map { |t| [t.humanize, t] }, @extraction_template.document_type),
                  { prompt: "Select document type..." },
                  class: "block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100" %>
            </div>
            
            <!-- Prompt Template -->
            <div>
              <%= form.label :prompt_template, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_area :prompt_template, 
                  rows: 6,
                  class: "block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100",
                  placeholder: "Enter the prompt template for AI extraction..." %>
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Use {{field_name}} for variables and {{fields_list}} for field descriptions
              </p>
            </div>
            
            <!-- Fields Configuration -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Fields to Extract
              </label>
              <div id="fields-container" class="space-y-3">
                <!-- Fields will be added dynamically -->
              </div>
              <button type="button" 
                      onclick="addField()"
                      class="mt-3 w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-coffee-800 transition-colors text-sm font-medium">
                + Add Field
              </button>
            </div>
            
            <!-- Settings -->
            <div class="pt-4 border-t border-gray-200 dark:border-coffee-800">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Settings</h4>
              <div class="space-y-3">
                <label class="flex items-center">
                  <%= check_box_tag "extraction_template[settings][auto_approve]", "true", 
                      @extraction_template.settings&.dig("auto_approve"),
                      class: "h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded" %>
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-approve high confidence extractions</span>
                </label>
                
                <label class="flex items-center">
                  <%= check_box_tag "extraction_template[settings][require_human_review]", "true",
                      @extraction_template.settings&.dig("require_human_review"),
                      class: "h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded" %>
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Require human review for all extractions</span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Form Actions -->
          <div class="mt-6 pt-6 border-t border-gray-200 dark:border-coffee-800 flex items-center justify-end space-x-3">
            <%= link_to "Cancel", extraction_templates_path, 
                class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-200" %>
            <%= form.submit "Create Template", 
                class: "px-4 py-2 bg-teal-600 dark:bg-teal-500 text-white text-sm font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
let fieldIndex = 0;

function addField() {
  const container = document.getElementById('fields-container');
  const fieldHtml = `
    <div class="flex items-start space-x-2 field-row">
      <input type="text" 
             name="extraction_template[fields][${fieldIndex}][name]" 
             placeholder="Field name"
             class="flex-1 px-3 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100 text-sm">
      <input type="text" 
             name="extraction_template[fields][${fieldIndex}][type]" 
             placeholder="Type (string, number, date)"
             class="flex-1 px-3 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100 text-sm">
      <label class="flex items-center">
        <input type="checkbox" 
               name="extraction_template[fields][${fieldIndex}][required]" 
               value="true"
               class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
        <span class="ml-1 text-sm text-gray-700 dark:text-gray-300">Required</span>
      </label>
      <button type="button" 
              onclick="removeField(this)"
              class="p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
      </button>
    </div>
  `;
  
  container.insertAdjacentHTML('beforeend', fieldHtml);
  fieldIndex++;
}

function removeField(button) {
  button.closest('.field-row').remove();
}

// Add initial field
addField();
</script>