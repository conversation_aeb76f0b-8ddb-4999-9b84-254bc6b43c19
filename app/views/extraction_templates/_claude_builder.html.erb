<!-- <PERSON> Code Template Builder -->
<div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6 mb-6">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
      AI Template Builder
    </h3>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-teal-100 dark:bg-teal-900/30 text-teal-800 dark:text-teal-200">
      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
      </svg>
      AI-Powered
    </span>
  </div>
  
  <div class="space-y-4">
    <!-- Sample Document Input -->
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Paste Sample Document Content
      </label>
      <textarea 
        id="sample-content"
        rows="6"
        class="block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100"
        placeholder="Paste a sample invoice, receipt, or any document text here..."></textarea>
    </div>
    
    <!-- Document Description -->
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Document Description
      </label>
      <input 
        type="text"
        id="document-description"
        class="block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100"
        placeholder="e.g., Restaurant receipt, Medical invoice, Bank statement">
    </div>
    
    <!-- Requirements -->
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Extraction Requirements (Optional)
      </label>
      <textarea 
        id="requirements"
        rows="3"
        class="block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100"
        placeholder="Describe any specific fields or rules you need..."></textarea>
    </div>
    
    <!-- Generate Button -->
    <button 
      onclick="generateTemplate()"
      class="w-full px-4 py-2 bg-teal-600 dark:bg-teal-500 text-white font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors flex items-center justify-center space-x-2">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
      <span>Generate Template with AI</span>
    </button>
  </div>
  
  <!-- Loading State -->
  <div id="loading-state" class="hidden mt-4">
    <div class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
      <span class="ml-3 text-gray-600 dark:text-gray-400">Claude is analyzing your document...</span>
    </div>
  </div>
  
  <!-- Generated Template Preview -->
  <div id="template-preview" class="hidden mt-6 p-4 bg-gray-50 dark:bg-coffee-900 rounded-lg">
    <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Generated Template</h4>
    <pre id="template-json" class="text-xs bg-white dark:bg-coffee-800 p-4 rounded overflow-x-auto"></pre>
    <button 
      onclick="useGeneratedTemplate()"
      class="mt-4 px-4 py-2 bg-teal-600 dark:bg-teal-500 text-white text-sm font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 transition-colors">
      Use This Template
    </button>
  </div>
</div>

<script>
async function generateTemplate() {
  const sampleContent = document.getElementById('sample-content').value;
  const description = document.getElementById('document-description').value;
  const requirements = document.getElementById('requirements').value;
  
  if (!sampleContent || !description) {
    alert('Please provide both sample content and description');
    return;
  }
  
  // Show loading state
  document.getElementById('loading-state').classList.remove('hidden');
  document.getElementById('template-preview').classList.add('hidden');
  
  try {
    const response = await fetch('/claude_code/generate_template', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({
        sample_content: sampleContent,
        description: description,
        requirements: requirements
      })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      // Show preview
      document.getElementById('template-json').textContent = JSON.stringify(data.template, null, 2);
      document.getElementById('template-preview').classList.remove('hidden');
      
      // Store for use
      window.generatedTemplate = data.template;
    } else {
      alert('Error: ' + data.message);
    }
  } catch (error) {
    alert('Failed to generate template: ' + error.message);
  } finally {
    document.getElementById('loading-state').classList.add('hidden');
  }
}

function useGeneratedTemplate() {
  if (window.generatedTemplate) {
    // Populate form fields with generated template
    // This would integrate with your existing template form
    alert('Template applied! Review and save your changes.');
  }
}
</script>