<%# 
  Status Card Component
  
  Parameters:
  - title: String - The title of the status card
  - icon: String - Icon type for the header
  - items: Array - Array of status items with { label:, value:, status:, percentage: }
  - animation_delay: String - CSS animation delay
%>

<div class="premium-card p-6 animate-slide-in-up" 
     style="animation-delay: <%= animation_delay || '0.5s' %>">
  <div class="flex items-center justify-between mb-6">
    <h3 class="section-title"><%= title %></h3>
    <div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <% case icon.to_sym %>
      <% when :processing %>
        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      <% when :users %>
        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
      <% when :performance %>
        <svg class="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      <% else %>
        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
      <% end %>
    </div>
  </div>
  
  <div class="space-y-4">
    <% items.each do |item| %>
      <% 
        # Determine status colors
        status_colors = case item[:status]&.to_s&.downcase
        when 'completed', 'success', 'good'
          {
            bg: 'bg-green-50 dark:bg-green-900/10',
            border: 'border-green-200 dark:border-green-800/30',
            text: 'text-green-800 dark:text-green-300',
            value: 'text-green-900 dark:text-green-200',
            icon_bg: 'bg-green-100 dark:bg-green-800/50',
            icon_text: 'text-green-600 dark:text-green-400',
            dot: 'bg-green-500'
          }
        when 'processing', 'warning', 'normal'
          {
            bg: 'bg-yellow-50 dark:bg-yellow-900/10',
            border: 'border-yellow-200 dark:border-yellow-800/30',
            text: 'text-yellow-800 dark:text-yellow-300',
            value: 'text-yellow-900 dark:text-yellow-200',
            icon_bg: 'bg-yellow-100 dark:bg-yellow-800/50',
            icon_text: 'text-yellow-600 dark:text-yellow-400',
            dot: 'bg-yellow-500'
          }
        when 'pending', 'info'
          {
            bg: 'bg-blue-50 dark:bg-blue-900/10',
            border: 'border-blue-200 dark:border-blue-800/30',
            text: 'text-blue-800 dark:text-blue-300',
            value: 'text-blue-900 dark:text-blue-200',
            icon_bg: 'bg-blue-100 dark:bg-blue-800/50',
            icon_text: 'text-blue-600 dark:text-blue-400',
            dot: 'bg-blue-500'
          }
        when 'failed', 'error', 'poor'
          {
            bg: 'bg-red-50 dark:bg-red-900/10',
            border: 'border-red-200 dark:border-red-800/30',
            text: 'text-red-800 dark:text-red-300',
            value: 'text-red-900 dark:text-red-200',
            icon_bg: 'bg-red-100 dark:bg-red-800/50',
            icon_text: 'text-red-600 dark:text-red-400',
            dot: 'bg-red-500'
          }
        else
          {
            bg: 'bg-gray-50 dark:bg-gray-900/10',
            border: 'border-gray-200 dark:border-gray-800/30',
            text: 'text-gray-800 dark:text-gray-300',
            value: 'text-gray-900 dark:text-gray-200',
            icon_bg: 'bg-gray-100 dark:bg-gray-800/50',
            icon_text: 'text-gray-600 dark:text-gray-400',
            dot: 'bg-gray-500'
          }
        end
      %>
      
      <div class="flex items-center justify-between p-3 <%= status_colors[:bg] %> rounded-lg border <%= status_colors[:border] %>">
        <div class="flex items-center">
          <% if item[:icon] %>
            <div class="p-2 <%= status_colors[:icon_bg] %> rounded-lg mr-3">
              <%= item[:icon].html_safe %>
            </div>
          <% else %>
            <div class="w-4 h-4 <%= status_colors[:dot] %> rounded-full mr-3 shadow-sm <%= 'animate-pulse' if item[:status] == 'processing' %>"></div>
          <% end %>
          <span class="font-medium <%= status_colors[:text] %>"><%= item[:label] %></span>
        </div>
        
        <div class="text-right">
          <span class="text-lg font-bold <%= status_colors[:value] %> number-display" 
                <% if item[:data_attribute] %>data-status="<%= item[:data_attribute] %>"<% end %>>
            <%= number_with_delimiter(item[:value]) %>
          </span>
          <% if item[:percentage] %>
            <div class="text-xs <%= status_colors[:text] %>">
              <%= item[:percentage] %>%
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
