<%# 
  KPI Card Component
  
  Parameters:
  - title: String - The title of the KPI
  - value: String/Number - The main value to display
  - subtitle: String - Additional context text
  - icon: String - Icon type (blue, green, purple, orange)
  - trend: Hash - Optional trend data { direction: :up/:down, value: String }
  - status: String - Optional status indicator
  - data_attribute: String - Data attribute for JavaScript targeting
  - animation_delay: String - CSS animation delay
%>

<% 
  icon_classes = {
    blue: "kpi-icon-blue",
    green: "kpi-icon-green", 
    purple: "kpi-icon-purple",
    orange: "kpi-icon-orange"
  }
  
  icon_colors = {
    blue: "text-blue-600 dark:text-blue-400",
    green: "text-green-600 dark:text-green-400",
    purple: "text-purple-600 dark:text-purple-400", 
    orange: "text-orange-600 dark:text-orange-400"
  }
%>

<div class="kpi-card interactive-element animate-slide-in-up" 
     style="animation-delay: <%= animation_delay || '0.1s' %>">
  <div class="flex items-center">
    <div class="kpi-icon-container <%= icon_classes[icon.to_sym] || icon_classes[:blue] %>">
      <% case icon.to_sym %>
      <% when :documents, :blue %>
        <svg class="w-7 h-7 <%= icon_colors[:blue] %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      <% when :success, :green %>
        <svg class="w-7 h-7 <%= icon_colors[:green] %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      <% when :users, :purple %>
        <svg class="w-7 h-7 <%= icon_colors[:purple] %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
      <% when :time, :orange %>
        <svg class="w-7 h-7 <%= icon_colors[:orange] %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      <% else %>
        <svg class="w-7 h-7 <%= icon_colors[:blue] %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      <% end %>
    </div>
    
    <div class="ml-5 flex-1">
      <p class="metric-label"><%= title %></p>
      <p class="metric-value text-gray-900 dark:text-white number-display" 
         <% if data_attribute %>data-metric="<%= data_attribute %>"<% end %>>
        <%= value %>
      </p>
      
      <div class="flex items-center mt-2">
        <span class="metric-subtitle">
          <%= subtitle %>
        </span>
        
        <% if trend %>
          <% if trend[:direction] == :up %>
            <svg class="w-3 h-3 ml-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
          <% elsif trend[:direction] == :down %>
            <svg class="w-3 h-3 ml-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          <% end %>
          <% if trend[:value] %>
            <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">
              <%= trend[:value] %>
            </span>
          <% end %>
        <% end %>
        
        <% if status %>
          <% 
            status_classes = case status.to_s.downcase
            when 'excellent', 'good'
              'status-completed'
            when 'normal', 'ok'
              'status-processing'
            when 'poor', 'slow', 'needs attention'
              'status-failed'
            else
              'status-pending'
            end
          %>
          <span class="ml-2 status-indicator <%= status_classes %>">
            <%= status %>
          </span>
        <% end %>
      </div>
    </div>
  </div>
</div>
