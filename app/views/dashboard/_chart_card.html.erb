<%# 
  Chart Card Component
  
  Parameters:
  - title: String - The title of the chart
  - icon: String - Icon type for the header
  - chart_type: String - Type of chart (line, bar, doughnut, pie)
  - chart_data: Hash - Chart.js data object
  - chart_options: Hash - Chart.js options object
  - height: String - Height class (default: h-64)
  - animation_delay: String - CSS animation delay
%>

<div class="premium-card p-6 animate-slide-in-up" 
     style="animation-delay: <%= animation_delay || '0.8s' %>">
  <div class="flex items-center justify-between mb-6">
    <h3 class="section-title"><%= title %></h3>
    <div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <% case icon.to_sym %>
      <% when :trends %>
        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      <% when :distribution %>
        <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
        </svg>
      <% when :performance %>
        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
      <% when :analytics %>
        <svg class="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      <% else %>
        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      <% end %>
    </div>
  </div>
  
  <div class="<%= height || 'h-64' %>" 
       data-controller="chart" 
       data-chart-type-value="<%= chart_type %>"
       data-chart-data-value='<%= chart_data_json(chart_data) %>'
       data-chart-options-value='<%= chart_data_json(chart_options || {}) %>'>
    <canvas data-chart-target="canvas"></canvas>
  </div>
</div>
