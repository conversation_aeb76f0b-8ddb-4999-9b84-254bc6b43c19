<div class="p-8">
  <h1 class="text-2xl font-bold mb-4">Theme Toggle Test Page</h1>
  
  <div class="mb-4">
    <p>Current theme: <span id="current-theme" class="font-mono"></span></p>
  </div>
  
  <div data-controller="theme" class="mb-4">
    <button data-action="click->theme#toggle" 
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
      Toggle Theme
      <span data-theme-target="icon" class="inline-block ml-2">
        <!-- Icon will be inserted here -->
      </span>
    </button>
  </div>
  
  <div class="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded">
    <p class="text-gray-900 dark:text-gray-100">This box should change color when you toggle the theme.</p>
  </div>
  
  <script>
    // Update current theme display
    function updateThemeDisplay() {
      const isDark = document.documentElement.classList.contains('dark');
      document.getElementById('current-theme').textContent = isDark ? 'dark' : 'light';
    }
    
    updateThemeDisplay();
    
    // Listen for theme changes
    const observer = new MutationObserver(updateThemeDisplay);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });
  </script>
</div>