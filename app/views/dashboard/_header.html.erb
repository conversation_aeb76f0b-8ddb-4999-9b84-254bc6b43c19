<%# 
  Dashboard Header Component
  
  Parameters:
  - user_name: String - Display name for the user
  - subtitle: String - Subtitle text
  - date_range_options: Array - Options for date range selector
  - current_date_range: String - Currently selected date range
%>

<div class="premium-card p-6 animate-fade-in-scale">
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <h1 class="dashboard-title mb-2">
        Welcome back, <%= user_name %>
      </h1>
      <p class="text-gray-600 dark:text-gray-400 text-lg">
        <%= subtitle || "Here's what's happening with your documents." %>
      </p>
      <div class="mt-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          <span>System Online</span>
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span>Last updated: <%= Time.current.strftime("%I:%M %p") %></span>
        </div>
      </div>
    </div>

    <!-- Date Range Selector -->
    <div class="mt-6 lg:mt-0 lg:ml-6">
      <%= form_with url: dashboard_path, method: :get, local: true, class: "inline-block" do |form| %>
        <div class="relative">
          <%= form.select :date_range,
                options_for_select(date_range_options, current_date_range),
                {},
                {
                  class: "premium-card px-4 py-2 pr-8 text-sm font-medium text-gray-700 dark:text-gray-300 border-0 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer",
                  onchange: "this.form.submit();"
                } %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
