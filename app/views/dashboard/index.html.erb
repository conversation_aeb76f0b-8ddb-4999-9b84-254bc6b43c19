<div class="dashboard-container p-6 space-y-8" data-controller="dashboard" data-dashboard-refresh-interval="30000">
  <!-- Header Section -->
  <% display_name = current_user.name.presence || current_user.email %>
  <%= render 'dashboard/header',
        user_name: display_name.to_s.split.first,
        subtitle: "Here's what's happening with your documents.",
        date_range_options: [
          ['Today', 'today'],
          ['Yesterday', 'yesterday'],
          ['Last 7 days', 'last_7_days'],
          ['Last 30 days', 'last_30_days'],
          ['Last 90 days', 'last_90_days'],
          ['This week', 'this_week'],
          ['Last week', 'last_week'],
          ['This month', 'this_month'],
          ['Last month', 'last_month']
        ],
        current_date_range: params[:date_range] || 'last_30_days' %>

  <!-- Primary KPI Cards -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-4">
    <!-- Total Documents -->
    <%= render 'dashboard/kpi_card',
          title: "Total Documents",
          value: number_with_delimiter(@metrics.dig(:overview, :total_documents) || 0),
          subtitle: "+#{number_with_delimiter(@metrics.dig(:overview, :documents_this_period) || 0)} this period",
          icon: :documents,
          trend: (@metrics.dig(:overview, :documents_this_period) || 0) > 0 ? { direction: :up } : nil,
          data_attribute: "total_documents",
          animation_delay: "0.1s" %>

    <!-- Success Rate -->
    <% success_rate = (@metrics.dig(:overview, :success_rate) || 0) %>
    <%= render 'dashboard/kpi_card',
          title: "Success Rate",
          value: "#{success_rate}%",
          subtitle: "Processing accuracy",
          icon: :success,
          status: success_rate >= 95 ? "Excellent" : (success_rate >= 85 ? "Good" : "Needs Attention"),
          data_attribute: "success_rate",
          animation_delay: "0.2s" %>

    <!-- Active Users -->
    <% engagement_rate = (@metrics.dig(:overview, :total_users) || 0) > 0 ? ((@metrics.dig(:overview, :active_users) || 0).to_f / (@metrics.dig(:overview, :total_users) || 1) * 100).round(1) : 0 %>
    <%= render 'dashboard/kpi_card',
          title: "Active Users",
          value: number_with_delimiter(@metrics.dig(:overview, :active_users) || 0),
          subtitle: "of #{number_with_delimiter(@metrics.dig(:overview, :total_users) || 0)} total (#{engagement_rate}%)",
          icon: :users,
          data_attribute: "active_users",
          animation_delay: "0.3s" %>

    <!-- Average Processing Time -->
    <% avg_time = (@metrics.dig(:overview, :avg_processing_time) || 0) %>
    <%= render 'dashboard/kpi_card',
          title: "Avg Processing",
          value: format_duration(avg_time),
          subtitle: "Per document",
          icon: :time,
          status: avg_time <= 30 ? "Fast" : (avg_time <= 60 ? "Normal" : "Slow"),
          data_attribute: "avg_processing_time",
          animation_delay: "0.4s" %>
  </div>

  <!-- Secondary Metrics Grid -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Document Processing Status -->
    <%
      completed = (@metrics.dig(:document_processing, :total_processed) || 0)
      processing = (@metrics.dig(:document_processing, :processing) || 0)
      pending = (@metrics.dig(:document_processing, :pending) || 0)
      failed = (@metrics.dig(:document_processing, :failed) || 0)
      total = completed + processing + pending + failed
    %>

    <%= render 'dashboard/status_card',
          title: "Processing Status",
          icon: :processing,
          animation_delay: "0.5s",
          items: [
            {
              label: "Completed",
              value: completed,
              status: "completed",
              percentage: total > 0 ? ((completed.to_f / total) * 100).round(1) : 0,
              data_attribute: "completed"
            },
            {
              label: "Processing",
              value: processing,
              status: "processing",
              percentage: total > 0 ? ((processing.to_f / total) * 100).round(1) : 0,
              data_attribute: "processing"
            },
            {
              label: "Pending",
              value: pending,
              status: "pending",
              percentage: total > 0 ? ((pending.to_f / total) * 100).round(1) : 0,
              data_attribute: "pending"
            },
            {
              label: "Failed",
              value: failed,
              status: "failed",
              percentage: total > 0 ? ((failed.to_f / total) * 100).round(1) : 0,
              data_attribute: "failed"
            }
          ] %>

    <!-- User Activity -->
    <div class="premium-card p-6 animate-slide-in-up" style="animation-delay: 0.6s">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">User Activity</h3>
        <div class="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
      </div>

      <div class="space-y-4">
        <!-- Active Today -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10 rounded-lg border border-blue-100 dark:border-blue-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="font-medium text-blue-800 dark:text-blue-300">Active Today</span>
          </div>
          <span class="text-lg font-bold text-blue-900 dark:text-blue-200" data-activity="active_today">
            <%= number_with_delimiter(@metrics.dig(:user_activity, :active_users_today) || 0) %>
          </span>
        </div>

        <!-- Active This Week -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/10 dark:to-emerald-900/10 rounded-lg border border-green-100 dark:border-green-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <span class="font-medium text-green-800 dark:text-green-300">Active This Week</span>
          </div>
          <span class="text-lg font-bold text-green-900 dark:text-green-200" data-activity="active_week">
            <%= number_with_delimiter(@metrics.dig(:user_activity, :active_users_this_week) || 0) %>
          </span>
        </div>

        <!-- Uploads Today -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/10 dark:to-amber-900/10 rounded-lg border border-orange-100 dark:border-orange-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 dark:bg-orange-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
            </div>
            <span class="font-medium text-orange-800 dark:text-orange-300">Uploads Today</span>
          </div>
          <span class="text-lg font-bold text-orange-900 dark:text-orange-200" data-activity="uploads_today">
            <%= number_with_delimiter(@metrics.dig(:user_activity, :documents_uploaded_today) || 0) %>
          </span>
        </div>

        <!-- Engagement Rate -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/10 rounded-lg border border-purple-100 dark:border-purple-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <span class="font-medium text-purple-800 dark:text-purple-300">Engagement Rate</span>
          </div>
          <span class="text-lg font-bold text-purple-900 dark:text-purple-200" data-activity="engagement_rate">
            <%= (@metrics.dig(:user_activity, :user_engagement, :engagement_rate) || 0) %>%
          </span>
        </div>
      </div>
    </div>

    <!-- System Performance -->
    <div class="premium-card p-6 animate-slide-in-up" style="animation-delay: 0.7s">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">System Performance</h3>
        <div class="p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
          <svg class="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
      </div>

      <div class="space-y-4">
        <!-- Queue Length -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/10 dark:to-gray-900/10 rounded-lg border border-slate-100 dark:border-slate-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-slate-100 dark:bg-slate-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
              </svg>
            </div>
            <span class="font-medium text-slate-800 dark:text-slate-300">Queue Length</span>
          </div>
          <span class="text-lg font-bold text-slate-900 dark:text-slate-200" data-performance="queue_length">
            <%= number_with_delimiter(@metrics.dig(:system_performance, :queue_stats, :total_pending) || 0) %>
          </span>
        </div>

        <!-- Processing Rate -->
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/10 dark:to-blue-900/10 rounded-lg border border-cyan-100 dark:border-cyan-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-cyan-100 dark:bg-cyan-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-cyan-600 dark:text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <span class="font-medium text-cyan-800 dark:text-cyan-300">Processing Rate</span>
          </div>
          <span class="text-lg font-bold text-cyan-900 dark:text-cyan-200" data-performance="processing_rate">
            <%= (@metrics.dig(:system_performance, :queue_stats, :processing_rate) || 0) %>/min
          </span>
        </div>

        <!-- Error Rate -->
        <% error_rate = (@metrics.dig(:system_performance, :error_rates, :error_rate) || 0) %>
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-50 to-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'orange' : 'emerald' %>-50 dark:from-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-900/10 dark:to-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'orange' : 'emerald' %>-900/10 rounded-lg border border-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-100 dark:border-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-100 dark:bg-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-600 dark:text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% if error_rate > 5 %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                <% elsif error_rate > 2 %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% end %>
              </svg>
            </div>
            <span class="font-medium text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-800 dark:text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-300">Error Rate</span>
          </div>
          <span class="text-lg font-bold text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-900 dark:text-<%= error_rate > 5 ? 'red' : error_rate > 2 ? 'yellow' : 'green' %>-200" data-performance="error_rate">
            <%= error_rate %>%
          </span>
        </div>

        <!-- API Response Time -->
        <% response_time = (@metrics.dig(:system_performance, :api_performance, :avg_response_time) || 0) %>
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-50 to-<%= response_time > 500 ? 'red' : response_time > 200 ? 'orange' : 'emerald' %>-50 dark:from-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-900/10 dark:to-<%= response_time > 500 ? 'red' : response_time > 200 ? 'orange' : 'emerald' %>-900/10 rounded-lg border border-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-100 dark:border-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-800/30">
          <div class="flex items-center">
            <div class="p-2 bg-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-100 dark:bg-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-800/50 rounded-lg mr-3">
              <svg class="w-4 h-4 text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-600 dark:text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="font-medium text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-800 dark:text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-300">API Response</span>
          </div>
          <span class="text-lg font-bold text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-900 dark:text-<%= response_time > 500 ? 'red' : response_time > 200 ? 'yellow' : 'green' %>-200" data-performance="api_response">
            <%= response_time %>ms
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Visualizations Section -->
  <div class="grid grid-cols-1 gap-6 xl:grid-cols-2">
    <!-- Document Processing Trends -->
    <div class="premium-card p-6 animate-slide-in-up" style="animation-delay: 0.8s">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Document Processing Trends</h3>
        <div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
      </div>

      <div class="h-64" data-controller="chart"
           data-chart-type-value="line"
           data-chart-data-value='<%= chart_data_json({
             labels: (@metrics.dig(:trends, :document_trends) || []).map { |d| Date.parse(d[:date]).strftime("%m/%d") rescue d[:date] },
             datasets: [{
               label: "Documents Processed",
               data: (@metrics.dig(:trends, :document_trends) || []).map { |d| d[:count] || 0 }
             }]
           }) %>'
           data-chart-options-value='<%= chart_data_json({
             plugins: {
               legend: { display: false }
             },
             scales: {
               y: {
                 title: {
                   display: true,
                   text: "Documents"
                 }
               }
             }
           }) %>'>
        <canvas data-chart-target="canvas"></canvas>
      </div>
    </div>

    <!-- Processing Status Distribution -->
    <div class="premium-card p-6 animate-slide-in-up" style="animation-delay: 0.9s">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Processing Status Distribution</h3>
        <div class="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
          </svg>
        </div>
      </div>

      <div class="h-64" data-controller="chart"
           data-chart-type-value="doughnut"
           data-chart-data-value='<%= chart_data_json({
             labels: ["Completed", "Processing", "Pending", "Failed"],
             datasets: [{
               data: [
                 (@metrics.dig(:document_processing, :total_processed) || 0),
                 (@metrics.dig(:document_processing, :processing) || 0),
                 (@metrics.dig(:document_processing, :pending) || 0),
                 (@metrics.dig(:document_processing, :failed) || 0)
               ]
             }]
           }) %>'
           data-chart-options-value='<%= chart_data_json({
             cutout: "60%",
             plugins: {
               legend: {
                 position: "bottom"
               }
             }
           }) %>'>
        <canvas data-chart-target="canvas"></canvas>
      </div>
    </div>
  </div>

  <!-- Success Rate Trends -->
  <div class="premium-card p-6 animate-slide-in-up" style="animation-delay: 1.0s">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-xl font-bold text-gray-900 dark:text-white">Success Rate & Performance Trends</h3>
      <div class="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
      </div>
    </div>

    <div class="h-80" data-controller="chart"
         data-chart-type-value="line"
         data-chart-data-value='<%= chart_data_json({
           labels: (@metrics.dig(:trends, :success_rate_trends) || []).map { |d| Date.parse(d[:date]).strftime("%m/%d") rescue d[:date] },
           datasets: [
             {
               label: "Success Rate (%)",
               data: (@metrics.dig(:trends, :success_rate_trends) || []).map { |d| d[:success_rate] || 0 },
               yAxisID: "y"
             },
             {
               label: "Avg Processing Time (s)",
               data: (@metrics.dig(:trends, :performance_trends) || []).map { |d| d[:avg_processing_time] || 0 },
               yAxisID: "y1"
             }
           ]
         }) %>'
         data-chart-options-value='<%= chart_data_json({
           scales: {
             y: {
               type: "linear",
               display: true,
               position: "left",
               title: {
                 display: true,
                 text: "Success Rate (%)"
               },
               max: 100
             },
             y1: {
               type: "linear",
               display: true,
               position: "right",
               title: {
                 display: true,
                 text: "Processing Time (s)"
               },
               grid: {
                 drawOnChartArea: false
               }
             }
           }
         }) %>'>
      <canvas data-chart-target="canvas"></canvas>
    </div>
  </div>
</div>