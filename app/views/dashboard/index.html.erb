<div class="p-6 space-y-6" data-controller="dashboard" data-dashboard-refresh-interval="30000">
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <% display_name = current_user.name.presence || current_user.email %>
      <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
        Welcome back, <%= display_name.to_s.split.first %>
      </h1>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
        Here's what's happening with your documents.
      </p>
    </div>

    <!-- Date Range Selector -->
    <div class="mt-4 sm:mt-0">
      <%= form_with url: dashboard_path, method: :get, local: true, class: "inline-block" do |form| %>
        <%= form.select :date_range,
              options_for_select([
                ['Today', 'today'],
                ['Yesterday', 'yesterday'],
                ['Last 7 days', 'last_7_days'],
                ['Last 30 days', 'last_30_days'],
                ['Last 90 days', 'last_90_days'],
                ['This week', 'this_week'],
                ['Last week', 'last_week'],
                ['This month', 'this_month'],
                ['Last month', 'last_month']
              ], params[:date_range] || 'last_30_days'),
              {},
              {
                class: "rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-sm",
                onchange: "this.form.submit();"
              } %>
      <% end %>
    </div>
  </div>

  <!-- Primary KPI Cards -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Documents -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
          <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Documents</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            <%= (@metrics.dig(:overview, :total_documents) || 0) %>
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            <%= (@metrics.dig(:overview, :documents_this_period) || 0) %> this period
          </p>
        </div>
      </div>
    </div>

    <!-- Success Rate -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
          <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            <%= (@metrics.dig(:overview, :success_rate) || 0) %>%
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Processing accuracy</p>
        </div>
      </div>
    </div>

    <!-- Active Users -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
          <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            <%= (@metrics.dig(:overview, :active_users) || 0) %>
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            of <%= (@metrics.dig(:overview, :total_users) || 0) %> total
          </p>
        </div>
      </div>
    </div>

    <!-- Average Processing Time -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-lg">
          <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Processing</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            <%= (@metrics.dig(:overview, :avg_processing_time) || 0) %>s
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Per document</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Secondary Metrics Grid -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Document Processing Status -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Processing Status</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:document_processing, :total_processed) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Processing</span>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:document_processing, :processing) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Pending</span>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:document_processing, :pending) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Failed</span>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:document_processing, :failed) || 0) %>
          </span>
        </div>
      </div>
    </div>

    <!-- User Activity -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">User Activity</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Active Today</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:user_activity, :active_users_today) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Active This Week</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:user_activity, :active_users_this_week) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Uploads Today</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:user_activity, :documents_uploaded_today) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Engagement Rate</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:user_activity, :user_engagement, :engagement_rate) || 0) %>%
          </span>
        </div>
      </div>
    </div>

    <!-- System Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Performance</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Queue Length</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:system_performance, :queue_stats, :total_pending) || 0) %>
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Processing Rate</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:system_performance, :queue_stats, :processing_rate) || 0) %>/min
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Error Rate</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:system_performance, :error_rates, :error_rate) || 0) %>%
          </span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">API Response</span>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            <%= (@metrics.dig(:system_performance, :api_performance, :avg_response_time) || 0) %>ms
          </span>
        </div>
      </div>
    </div>
  </div>
</div>