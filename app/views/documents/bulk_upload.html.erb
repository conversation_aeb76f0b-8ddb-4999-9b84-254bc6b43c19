<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="max-w-4xl mx-auto">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Bulk Upload Documents</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Upload multiple documents at once for batch processing
      </p>
    </div>

    <div data-controller="bulk-upload" data-bulk-upload-max-files-value="100" data-bulk-upload-max-size-value="10485760">
      <%= form_with url: bulk_create_documents_path, local: false, data: { 
        "bulk-upload-target": "form",
        "action": "submit->bulk-upload#submit"
      } do |f| %>
        
        <!-- Template Selection -->
        <div class="mb-6 bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 p-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Extraction Template (Optional)
          </label>
          <%= select_tag :extraction_template_id, 
              options_from_collection_for_select(@extraction_templates, :id, :name),
              include_blank: "Select a template...",
              class: "block w-full rounded-lg border-gray-300 dark:border-coffee-700 dark:bg-coffee-900 dark:text-gray-100 shadow-sm focus:border-teal-500 focus:ring-teal-500 sm:text-sm",
              data: { "bulk-upload-target": "templateSelect" } %>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Documents will be processed using the selected template
          </p>
        </div>

        <!-- Drag & Drop Area -->
        <div class="mb-6">
          <div data-bulk-upload-target="dropzone"
               data-action="drop->bulk-upload#handleDrop dragover->bulk-upload#handleDragOver dragleave->bulk-upload#handleDragLeave"
               class="relative border-2 border-dashed border-gray-300 dark:border-coffee-700 rounded-lg p-12 text-center hover:border-gray-400 dark:hover:border-coffee-600 transition-colors">
            
            <input type="file" 
                   name="files[]" 
                   multiple 
                   accept=".pdf,.png,.jpg,.jpeg,.doc,.docx"
                   data-bulk-upload-target="fileInput"
                   data-action="change->bulk-upload#handleFileSelect"
                   class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
            
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              <span class="font-medium">Click to upload</span> or drag and drop
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-500">
              PDF, PNG, JPG, DOC, DOCX up to 10MB each
            </p>
          </div>
        </div>

        <!-- File List -->
        <div data-bulk-upload-target="fileList" class="hidden mb-6">
          <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700 flex items-center justify-between">
              <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">
                Selected Files (<span data-bulk-upload-target="fileCount">0</span>)
              </h3>
              <button type="button" 
                      data-action="click->bulk-upload#clearAll"
                      class="text-sm text-red-600 hover:text-red-500 dark:text-red-400">
                Clear All
              </button>
            </div>
            <div class="px-6 py-4">
              <ul data-bulk-upload-target="files" class="space-y-2">
                <!-- Files will be added here dynamically -->
              </ul>
            </div>
          </div>
        </div>

        <!-- Upload Progress -->
        <div data-bulk-upload-target="progress" class="hidden mb-6">
          <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Uploading...</span>
              <span data-bulk-upload-target="progressText" class="text-sm text-gray-500 dark:text-gray-400">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div data-bulk-upload-target="progressBar" 
                   class="bg-teal-600 h-2 rounded-full transition-all duration-300" 
                   style="width: 0%"></div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end space-x-3">
          <%= link_to "Cancel", documents_path, class: "px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-coffee-800 hover:bg-gray-50 dark:hover:bg-coffee-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500" %>
          <button type="submit" 
                  data-bulk-upload-target="submitButton"
                  disabled
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload Documents
          </button>
        </div>
      <% end %>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-gray-50 dark:bg-coffee-900/50 rounded-lg p-6">
      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Bulk Upload Tips</h3>
      <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          You can upload up to 100 files at once
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Each file must be under 10MB in size
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Select a template to automatically process documents after upload
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Drag folders to upload all documents inside them
        </li>
      </ul>
    </div>
  </div>
</div>