<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Start your free trial
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        14-day free trial. No credit card required.
      </p>
    </div>
    
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
                 html: { class: "mt-8 space-y-6", data: { turbo: false, controller: "registration" } }) do |f| %>
      
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <%= render "devise/shared/error_messages", resource: resource %>
        
        <div class="space-y-6">
          <!-- Organization Info -->
          <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-900">Organization Information</h3>
            
            <%= fields_for :tenant do |t| %>
              <div class="mt-4">
                <%= t.label :name, "Organization Name", class: "block text-sm font-medium text-gray-700" %>
                <%= t.text_field :name, 
                    class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    placeholder: "Acme Corp" %>
              </div>
              
              <div class="mt-4">
                <%= t.label :subdomain, class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1 flex rounded-md shadow-sm">
                  <%= t.text_field :subdomain,
                      class: "flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                      placeholder: "acme",
                      data: {
                        registration_target: "subdomain",
                        action: "input->registration#normalizeSubdomain"
                      } %>
                  <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    .docutiz.com
                  </span>
                </div>
                <p class="mt-1 text-xs text-gray-500">Only lowercase letters and numbers allowed</p>
              </div>
            <% end %>
          </div>
          
          <!-- User Info -->
          <div>
            <h3 class="text-lg font-medium text-gray-900">Your Information</h3>
            
            <div class="mt-4">
              <%= f.label :name, class: "block text-sm font-medium text-gray-700" %>
              <%= f.text_field :name, 
                  autofocus: true,
                  class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  placeholder: "John Doe" %>
            </div>
            
            <div class="mt-4">
              <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
              <%= f.email_field :email, 
                  autocomplete: "email",
                  class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  placeholder: "<EMAIL>" %>
            </div>
            
            <div class="mt-4">
              <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :password, 
                  autocomplete: "new-password",
                  class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              <% if @minimum_password_length %>
                <p class="mt-1 text-xs text-gray-500">
                  <%= @minimum_password_length %> characters minimum
                </p>
              <% end %>
            </div>
            
            <div class="mt-4">
              <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700" %>
              <%= f.password_field :password_confirmation, 
                  autocomplete: "new-password",
                  class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>
          </div>
        </div>
        
        <div class="mt-6">
          <%= f.submit "Start Free Trial", 
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
        
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Already have an account?</span>
            </div>
          </div>
          
          <div class="mt-6">
            <%= link_to "Sign in to existing account", new_session_path(resource_name), 
                class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>