<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Sign in to your account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        <% if request.subdomain.present? && request.subdomain != 'www' %>
          Signing in to <span class="font-medium"><%= request.subdomain %>.docutiz.com</span>
        <% else %>
          Please use your organization's subdomain to sign in
        <% end %>
      </p>
    </div>
    
    <% form_url = if request.subdomain.present? && request.subdomain != 'www'
                    session_url(resource_name, subdomain: request.subdomain)
                  else
                    session_path(resource_name)
                  end %>
    <%= form_for(resource, as: resource_name, url: form_url,
                 html: { class: "mt-8 space-y-6", data: { turbo: false } }) do |f| %>
      
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <% if flash[:alert] %>
          <div class="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <p class="text-sm text-red-600"><%= flash[:alert] %></p>
          </div>
        <% end %>
        
        <div class="space-y-4">
          <div>
            <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
            <%= f.email_field :email, 
                autofocus: true, 
                autocomplete: "email",
                class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                placeholder: "<EMAIL>" %>
          </div>
          
          <div>
            <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
            <%= f.password_field :password, 
                autocomplete: "current-password",
                class: "mt-1 appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
          </div>
          
          <div class="flex items-center justify-between">
            <% if devise_mapping.rememberable? %>
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-900" %>
              </div>
            <% end %>
            
            <div class="text-sm">
              <%= link_to "Forgot your password?", new_password_path(resource_name),
                          class: "font-medium text-indigo-600 hover:text-indigo-500" %>
            </div>
          </div>
        </div>
        
        <div class="mt-6">
          <%= f.submit "Sign in", 
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
        
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Don't have an account?</span>
            </div>
          </div>
          
          <div class="mt-6">
            <%= link_to "Start your free trial", new_registration_path(resource_name),
                class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" %>
          </div>
        </div>
        
        <% if !request.subdomain.present? || request.subdomain == 'www' %>
          <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <p class="text-sm text-yellow-700">
              <strong>Note:</strong> To sign in, please use your organization's subdomain URL (e.g., yourcompany.docutiz.com)
            </p>
            <div class="mt-4">
              <%= form_tag new_user_session_path, method: :get, class: "flex gap-2" do %>
                <%= text_field_tag :subdomain, "", 
                    placeholder: "yourcompany", 
                    class: "flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                <%= submit_tag "Go to subdomain", 
                    class: "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>