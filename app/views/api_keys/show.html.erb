<div class="px-4 py-8 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">API Access</h1>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your API key for programmatic access to Docutiz</p>
  </div>

  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- API Key Section -->
    <div class="lg:col-span-2">
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border">
        <div class="px-6 py-4 border-b border-gray-100 dark:border-dark-border">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Your API Key</h3>
        </div>
        <div class="p-6">
          <% if flash[:api_token].present? %>
            <!-- New Token Alert -->
            <div class="mb-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">New API Key Generated</h4>
                  <p class="text-sm text-amber-700 dark:text-amber-300 mb-3">
                    Save this key now! For security reasons, it won't be shown again.
                  </p>
                  <div class="flex items-center space-x-2">
                    <input type="text" 
                           value="<%= flash[:api_token] %>" 
                           id="new-api-token"
                           readonly
                           class="flex-1 px-4 py-2 border border-amber-300 dark:border-amber-700 rounded-lg bg-white dark:bg-coffee-800 text-gray-900 dark:text-gray-100 font-mono text-sm">
                    <button onclick="copyNewApiKey()" 
                            class="px-4 py-2 bg-amber-600 dark:bg-amber-700 text-white rounded-lg hover:bg-amber-700 dark:hover:bg-amber-800 transition-colors">
                      Copy
                    </button>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          
          <% if @user.api_token_digest.present? %>
            <div class="space-y-4">
              <!-- API Token Display -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Key
                </label>
                <div class="flex items-center space-x-2">
                  <input type="text" 
                         value="doc_••••••••••••••••••••••••••••••••••••••••••••••••" 
                         id="api-token"
                         readonly
                         disabled
                         class="flex-1 px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg bg-gray-50 dark:bg-coffee-900 text-gray-500 dark:text-gray-500 font-mono text-sm">
                </div>
              </div>

              <!-- Usage Stats -->
              <div class="grid grid-cols-2 gap-4 pt-4">
                <div class="bg-gray-50 dark:bg-coffee-900 rounded-lg p-4">
                  <p class="text-sm text-gray-600 dark:text-gray-400">API Requests</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @user.api_requests_count %></p>
                </div>
                <div class="bg-gray-50 dark:bg-coffee-900 rounded-lg p-4">
                  <p class="text-sm text-gray-600 dark:text-gray-400">Last Used</p>
                  <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= @user.api_token_last_used_at ? time_ago_in_words(@user.api_token_last_used_at) + ' ago' : 'Never' %>
                  </p>
                </div>
              </div>

              <!-- Regenerate Button -->
              <div class="pt-4 border-t border-gray-200 dark:border-coffee-700">
                <div class="flex items-start space-x-3">
                  <svg class="w-5 h-5 text-amber-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      Keep your API key secure. Don't share it publicly or commit it to version control.
                    </p>
                    <%= button_to regenerate_api_key_path, method: :post, 
                                  data: { turbo_confirm: 'Are you sure? This will invalidate your current API key and all integrations using it will stop working.' },
                                  class: "mt-3 inline-flex items-center px-4 py-2 bg-red-600 dark:bg-red-500 text-white text-sm font-medium rounded-lg hover:bg-red-700 dark:hover:bg-red-600 transition-colors" do %>
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      Regenerate API Key
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          <% else %>
            <div class="text-center py-8">
              <p class="text-gray-500 dark:text-gray-400 mb-4">No API key generated yet</p>
              <%= button_to regenerate_api_key_path, method: :post,
                            class: "inline-flex items-center px-4 py-2 bg-teal-600 dark:bg-teal-500 text-white font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 transition-colors" do %>
                Generate API Key
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Documentation Links -->
    <div class="lg:col-span-1">
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Start</h3>
        
        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">1. Set Authorization Header</h4>
            <pre class="bg-gray-50 dark:bg-coffee-900 p-3 rounded text-xs overflow-x-auto">Authorization: Bearer your_api_key</pre>
          </div>
          
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">2. Upload a Document</h4>
            <pre class="bg-gray-50 dark:bg-coffee-900 p-3 rounded text-xs overflow-x-auto">POST /api/v1/document_extractions
Content-Type: multipart/form-data

document[file]=@invoice.pdf</pre>
          </div>
          
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">3. Get Results</h4>
            <pre class="bg-gray-50 dark:bg-coffee-900 p-3 rounded text-xs overflow-x-auto">GET /api/v1/document_extractions/:id</pre>
          </div>
        </div>
        
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-coffee-700">
          <%= link_to claude_code_api_builder_path, class: "inline-flex items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
            Generate Custom API Code
          <% end %>
        </div>
      </div>
      
      <!-- Rate Limits -->
      <div class="mt-6 bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Rate Limits</h3>
        
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Current Plan</span>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Pro</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Requests/Minute</span>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">500</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Requests/Day</span>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">50,000</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function copyApiKey() {
  const apiTokenInput = document.getElementById('api-token');
  apiTokenInput.select();
  document.execCommand('copy');
  
  // Show feedback
  const button = event.currentTarget;
  const originalHTML = button.innerHTML;
  button.innerHTML = '<svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
  
  setTimeout(() => {
    button.innerHTML = originalHTML;
  }, 2000);
}
function copyNewApiKey() {
  const apiTokenInput = document.getElementById('new-api-token');
  apiTokenInput.select();
  document.execCommand('copy');
  
  // Show feedback
  const button = event.currentTarget;
  const originalHTML = button.innerHTML;
  button.innerHTML = 'Copied!';
  
  setTimeout(() => {
    button.innerHTML = originalHTML;
  }, 2000);
}
</script>