<div class="px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="sm:flex sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Team Members</h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Manage your team members and their roles
        </p>
      </div>
      <div class="mt-4 sm:mt-0 flex items-center space-x-3">
        <%= link_to activity_team_index_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-coffee-800 hover:bg-gray-50 dark:hover:bg-coffee-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200" do %>
          <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Activity Log
        <% end %>
        
        <% if current_user.can_manage_users? %>
          <%= link_to new_team_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200" do %>
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Invite Member
          <% end %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Team Stats -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <div class="bg-white dark:bg-coffee-800 rounded-lg shadow-sm border border-gray-200 dark:border-coffee-700 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
          <svg class="h-6 w-6 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <div class="ml-5">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Members</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @stats[:total_members] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 rounded-lg shadow-sm border border-gray-200 dark:border-coffee-700 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
          <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <div class="ml-5">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Owners</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @stats[:owners] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 rounded-lg shadow-sm border border-gray-200 dark:border-coffee-700 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>
        <div class="ml-5">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Admins</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @stats[:admins] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 rounded-lg shadow-sm border border-gray-200 dark:border-coffee-700 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
          <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        <div class="ml-5">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Members</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @stats[:members] %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Pending Invitations -->
  <% if @pending_invitations.any? %>
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Pending Invitations</h2>
      <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 overflow-hidden">
        <ul class="divide-y divide-gray-200 dark:divide-coffee-700">
          <% @pending_invitations.each do |invitation| %>
            <li class="px-6 py-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-coffee-700 flex items-center justify-center">
                      <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      <%= invitation.name || invitation.email %>
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      <%= invitation.email %> • 
                      Role: <%= invitation.role.capitalize %> • 
                      Invited by <%= invitation.invited_by.name %> 
                      <%= time_ago_in_words(invitation.created_at) %> ago
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <% if current_user.can_manage_users? %>
                    <%= link_to resend_invitation_team_path(invitation), method: :post, class: "text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300" do %>
                      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    <% end %>
                    <%= link_to cancel_invitation_team_index_path(id: invitation.id), method: :post, data: { confirm: "Are you sure?" }, class: "text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300" do %>
                      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <!-- Team Members -->
  <div class="bg-white dark:bg-coffee-800 shadow-sm rounded-lg border border-gray-200 dark:border-coffee-700 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
      <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100">Team Members</h3>
    </div>
    <ul class="divide-y divide-gray-200 dark:divide-coffee-700">
      <% @users.each do |user| %>
        <li class="px-6 py-4 hover:bg-gray-50 dark:hover:bg-coffee-900 transition-colors duration-150">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-teal-500 flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    <%= user.name.split.map(&:first).join.upcase %>
                  </span>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  <%= user.name %>
                  <% if user == current_user %>
                    <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(You)</span>
                  <% end %>
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <%= user.email %> • 
                  <%= user.documents.count %> documents • 
                  Joined <%= user.created_at.strftime("%B %Y") %>
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.owner? ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' : user.admin? ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                <%= user.role.capitalize %>
              </span>
              
              <% if current_user.can_manage_users? && user != current_user %>
                <div class="relative" data-controller="dropdown">
                  <button type="button" 
                          data-action="click->dropdown#toggle"
                          class="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                  
                  <div data-dropdown-target="menu"
                       class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-coffee-800 ring-1 ring-black ring-opacity-5 z-10">
                    <div class="py-1">
                      <%= link_to "View Profile", team_path(user), class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                      <%= link_to "Edit Member", edit_team_path(user), class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                      <div class="border-t border-gray-100 dark:border-coffee-700"></div>
                      <%= link_to "Remove Member", team_path(user), method: :delete, data: { confirm: "Are you sure?" }, class: "block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20" %>
                    </div>
                  </div>
                </div>
              <% else %>
                <%= link_to team_path(user), class: "text-gray-400 hover:text-gray-500 dark:hover:text-gray-300" do %>
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                <% end %>
              <% end %>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
  </div>
</div>

<script>
// Dropdown controller
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('[data-controller="dropdown"]').forEach(function(dropdown) {
    const button = dropdown.querySelector('[data-action="click->dropdown#toggle"]');
    const menu = dropdown.querySelector('[data-dropdown-target="menu"]');
    
    if (button && menu) {
      button.addEventListener('click', function(e) {
        e.stopPropagation();
        
        // Close other dropdowns
        document.querySelectorAll('[data-dropdown-target="menu"]').forEach(function(otherMenu) {
          if (otherMenu !== menu) {
            otherMenu.classList.add('hidden');
          }
        });
        
        menu.classList.toggle('hidden');
      });
      
      document.addEventListener('click', function() {
        menu.classList.add('hidden');
      });
      
      menu.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    }
  });
});
</script>