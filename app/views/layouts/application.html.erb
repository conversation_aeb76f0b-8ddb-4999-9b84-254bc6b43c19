<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Docutiz - AI-Powered Document Extraction Platform" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Transform unstructured documents into actionable data with AI-powered extraction. Reduce manual data entry by 90% with enterprise-grade accuracy.">
    <meta name="keywords" content="document extraction, AI, OCR, invoice processing, document automation">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<%= content_for(:title) || 'Docutiz - AI Document Extraction' %>">
    <meta property="og:description" content="AI-powered document extraction platform for modern businesses">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="antialiased font-sans flex flex-col min-h-screen bg-gray-50">
    <script>
      // Apply theme immediately to prevent flash
      (function() {
        const savedTheme = localStorage.getItem('theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const theme = savedTheme || systemTheme;
        
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    <main class="min-h-screen flex flex-col">
      <%= yield %>
    </main>
    <% if request.subdomain.present? && request.subdomain != 'www' %>
      <!-- Dashboard/Tenant pages -->
      <%= render 'shared/dashboard_footer' %>
    <% else %>
      <!-- Marketing pages -->
      <%= render 'shared/footer' %>
    <% end %>
  </body>
</html>