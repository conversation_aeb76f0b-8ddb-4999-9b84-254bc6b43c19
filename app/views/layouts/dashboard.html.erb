<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Docutiz" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="antialiased bg-gray-50 dark:bg-dark-bg text-gray-900 dark:text-gray-100">
    <script>
      // Apply theme immediately to prevent flash
      (function() {
        const savedTheme = localStorage.getItem('theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const theme = savedTheme || systemTheme;
        
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    <div class="min-h-screen flex">
      <!-- Sidebar -->
      <div class="fixed inset-y-0 left-0 z-50 w-64 transition-transform -translate-x-full bg-gray-900 dark:bg-dark-surface lg:translate-x-0 lg:static lg:inset-0">
        <div class="flex flex-col h-full">
          <!-- Logo -->
          <div class="flex items-center h-16 px-6 bg-gray-800 dark:bg-coffee-950">
            <h1 class="text-xl font-bold text-white">Docutiz</h1>
          </div>

          <!-- Navigation -->
          <nav class="flex-1 px-4 py-6 space-y-1">
            <%= link_to dashboard_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ current_page?(dashboard_path) ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Dashboard
            <% end %>

            <%= link_to documents_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'documents' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Documents
            <% end %>

            <%= link_to extraction_templates_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'extraction_templates' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
              </svg>
              Templates
            <% end %>

            <%= link_to team_index_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'team' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Team
            <% end %>

            <%= link_to "/claude_code/api_builder", class: "flex items-center px-4 py-2.5 text-sm font-medium text-gray-300 rounded-lg transition-colors hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              API Builder
            <% end %>
            
            <%= link_to api_key_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'api_keys' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
              API Keys
            <% end %>
            
            <%= link_to webhooks_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'webhooks' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Webhooks
            <% end %>
            
            <% if current_user.can_manage_users? %>
              <%= link_to document_queues_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'document_queues' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Queue Dashboard
              <% end %>
            <% end %>
            
            <%= link_to settings_path, class: "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors #{ controller_name == 'settings' ? 'bg-gray-800 dark:bg-coffee-800 text-white' : 'text-gray-300 hover:bg-gray-800 dark:hover:bg-coffee-800 hover:text-white' }" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Settings
            <% end %>
          </nav>

          <!-- User Menu -->
          <div class="p-4 border-t border-gray-800 dark:border-coffee-800">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-gray-700 dark:bg-coffee-700 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-white"><%= current_user.name.split.map(&:first).join.upcase %></span>
                </div>
              </div>
              <div class="ml-3 flex-1">
                <p class="text-sm font-medium text-white"><%= current_user.name %></p>
                <p class="text-xs text-gray-400"><%= current_user.email %></p>
              </div>
              <%= button_to destroy_user_session_path, method: :delete, data: { turbo: false }, class: "ml-2 p-2 text-gray-400 hover:text-white transition-colors" do %>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col">
        <!-- Top Bar -->
        <header class="bg-white dark:bg-dark-surface border-b border-gray-200 dark:border-dark-border">
          <div class="px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
            <!-- Mobile menu button -->
            <button type="button" class="lg:hidden p-2 rounded-md text-gray-400 dark:text-gray-300 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-coffee-800">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <!-- Search -->
            <div class="flex-1 max-w-lg mx-4">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input type="search" placeholder="Search documents..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg leading-5 bg-gray-50 dark:bg-coffee-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-teal-500 focus:border-teal-500 sm:text-sm">
              </div>
            </div>

            <!-- Right side buttons -->
            <div class="flex items-center space-x-4">
              <!-- Theme Toggle -->
              <div data-controller="theme">
                <button type="button" 
                        data-action="click->theme#toggle"
                        data-theme-toggle
                        class="p-2 text-gray-400 dark:text-gray-300 hover:text-gray-500 dark:hover:text-gray-200">
                  <span data-theme-target="icon" data-theme-icon>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                  </span>
                </button>
              </div>
              
              <button type="button" class="p-2 text-gray-400 dark:text-gray-300 hover:text-gray-500 dark:hover:text-gray-200">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </button>
            </div>
          </div>
        </header>

        <!-- Page Content -->
        <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-bg">
          <% if notice.present? %>
            <div class="mx-4 mt-4 sm:mx-6 lg:mx-8">
              <div class="rounded-lg bg-green-50 dark:bg-green-900/20 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800 dark:text-green-200"><%= notice %></p>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          
          <% if alert.present? %>
            <div class="mx-4 mt-4 sm:mx-6 lg:mx-8">
              <div class="rounded-lg bg-red-50 dark:bg-red-900/20 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-red-800 dark:text-red-200"><%= alert %></p>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <%= yield %>
        </main>
      </div>
    </div>
  </body>
</html>