<div class="px-4 py-8 sm:px-6 lg:px-8" data-controller="claude-code">
  <!-- Header -->
  <div class="mb-8 flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">API Builder</h1>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Generate production-ready API endpoints with AI assistance</p>
    </div>
    <button type="button" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-coffee-800 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg hover:bg-gray-200 dark:hover:bg-coffee-700 transition-colors">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
      View Documentation
    </button>
  </div>

  <!-- Quick Start Templates -->
  <div class="mb-8">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Start Templates</h2>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <!-- REST API Template -->
      <button onclick="loadTemplate('rest')" class="group bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md hover:border-teal-200 dark:hover:border-teal-600 transition-all text-left">
        <div class="flex items-center">
          <div class="p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg group-hover:bg-teal-100 dark:group-hover:bg-teal-800/30 transition-colors">
            <svg class="w-6 h-6 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900 dark:text-gray-100">REST API</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Standard CRUD endpoints</p>
          </div>
        </div>
      </button>

      <!-- Webhook Handler Template -->
      <button onclick="loadTemplate('webhook')" class="group bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md hover:border-purple-200 dark:hover:border-purple-600 transition-all text-left">
        <div class="flex items-center">
          <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg group-hover:bg-purple-100 dark:group-hover:bg-purple-800/30 transition-colors">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900 dark:text-gray-100">Webhook Handler</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Receive external events</p>
          </div>
        </div>
      </button>

      <!-- Data Transformer Template -->
      <button onclick="loadTemplate('transformer')" class="group bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md hover:border-blue-200 dark:hover:border-blue-600 transition-all text-left">
        <div class="flex items-center">
          <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg group-hover:bg-blue-100 dark:group-hover:bg-blue-800/30 transition-colors">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900 dark:text-gray-100">Data Transformer</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">ETL operations</p>
          </div>
        </div>
      </button>

      <!-- Custom Integration Template -->
      <button onclick="loadTemplate('custom')" class="group bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md hover:border-amber-200 dark:hover:border-amber-600 transition-all text-left">
        <div class="flex items-center">
          <div class="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg group-hover:bg-amber-100 dark:group-hover:bg-amber-800/30 transition-colors">
            <svg class="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900 dark:text-gray-100">Custom Integration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Business-specific API</p>
          </div>
        </div>
      </button>
    </div>
  </div>

  <!-- API Builder Interface -->
  <div class="relative">
    <!-- Initial State - Show when no template is selected -->
    <div id="initial-state" class="block">
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-12">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
          </svg>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">Start Building Your API</h3>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Choose a template above or describe your custom requirements below</p>
          <button onclick="showCustomForm()" class="mt-6 inline-flex items-center px-4 py-2 bg-teal-600 dark:bg-teal-500 text-white text-sm font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Custom API
          </button>
        </div>
      </div>
    </div>

    <!-- API Configuration Form - Hidden by default -->
    <div id="api-form" class="hidden">
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">API Configuration</h3>
        
        <div class="space-y-6">
          <!-- Resource Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Resource Name
            </label>
            <input type="text" 
                   id="resource-name"
                   placeholder="e.g., webhook_endpoints, invoices, documents"
                   class="block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400">
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Use snake_case, plural form (e.g., user_accounts)</p>
          </div>
          
          <!-- Requirements -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Describe Your API Requirements
            </label>
            <textarea id="api-requirements"
                      rows="6"
                      placeholder="I need an API that:
• Accepts document uploads (PDF, images, etc.)
• Extracts specific fields from invoices/receipts
• Returns structured JSON with extracted data
• Includes confidence scores for each field
• Supports batch document processing
• Sends webhooks when extraction is complete"
                      class="block w-full px-4 py-2 border border-gray-300 dark:border-coffee-700 rounded-lg focus:ring-teal-500 focus:border-teal-500 dark:bg-coffee-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"></textarea>
          </div>
          
          <!-- Features -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Include Features
            </label>
            <div class="space-y-3">
              <label class="flex items-center">
                <input type="checkbox" checked id="include-auth" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">Authentication & Authorization</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" checked id="include-tests" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">Comprehensive Test Suite</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" checked id="include-docs" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">API Documentation</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" id="include-webhooks" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">Webhook Events</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" id="include-versioning" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">API Versioning</span>
              </label>
            </div>
          </div>
          
          <!-- Generate Button -->
          <div class="flex items-center justify-between pt-4">
            <button onclick="hideApiForm()" class="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors">
              Cancel
            </button>
            <button type="button"
                    id="generate-api-btn"
                    data-action="click->claude-code#generateAPI"
                    class="inline-flex items-center px-6 py-2 bg-teal-600 dark:bg-teal-500 text-white font-medium rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              Generate API Code
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div data-claude-code-target="loading" class="hidden">
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-12">
        <div class="flex flex-col items-center justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
          <p class="mt-4 text-gray-600 dark:text-gray-400">Claude is generating your API code...</p>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">This usually takes 10-30 seconds</p>
        </div>
      </div>
    </div>

    <!-- Results Section -->
    <div data-claude-code-target="result" class="hidden">
      <!-- Results Navigation -->
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border mb-6 p-2">
        <nav class="flex space-x-2">
          <button onclick="showTab('controller')" id="tab-controller" class="tab-button px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300">
            Controller
          </button>
          <button onclick="showTab('serializer')" id="tab-serializer" class="tab-button px-4 py-2 text-sm font-medium rounded-lg transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
            Serializer
          </button>
          <button onclick="showTab('tests')" id="tab-tests" class="tab-button px-4 py-2 text-sm font-medium rounded-lg transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
            Tests
          </button>
          <button onclick="showTab('docs')" id="tab-docs" class="tab-button px-4 py-2 text-sm font-medium rounded-lg transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
            Documentation
          </button>
        </nav>
      </div>

      <!-- Code Display -->
      <div class="bg-white dark:bg-dark-surface rounded-xl shadow-sm border border-gray-100 dark:border-dark-border overflow-hidden">
        <!-- Controller Tab -->
        <div id="controller-tab" class="code-tab">
          <div class="px-6 py-4 border-b border-gray-100 dark:border-dark-border flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">app/controllers/api/v1/resource_controller.rb</h4>
            </div>
            <button data-action="click->claude-code#copyToClipboard" data-code-type="controller"
                    class="inline-flex items-center px-3 py-1 text-sm text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Copy
            </button>
          </div>
          <div class="p-6">
            <pre id="controller-code" class="text-sm bg-gray-50 dark:bg-coffee-900 p-4 rounded-lg overflow-x-auto font-mono text-gray-800 dark:text-gray-200"></pre>
          </div>
        </div>

        <!-- Serializer Tab -->
        <div id="serializer-tab" class="code-tab hidden">
          <div class="px-6 py-4 border-b border-gray-100 dark:border-dark-border flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">app/serializers/resource_serializer.rb</h4>
            </div>
            <button data-action="click->claude-code#copyToClipboard" data-code-type="serializer"
                    class="inline-flex items-center px-3 py-1 text-sm text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Copy
            </button>
          </div>
          <div class="p-6">
            <pre id="serializer-code" class="text-sm bg-gray-50 dark:bg-coffee-900 p-4 rounded-lg overflow-x-auto font-mono text-gray-800 dark:text-gray-200"></pre>
          </div>
        </div>

        <!-- Tests Tab -->
        <div id="tests-tab" class="code-tab hidden">
          <div class="px-6 py-4 border-b border-gray-100 dark:border-dark-border flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
              </svg>
              <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">spec/requests/api/v1/resource_spec.rb</h4>
            </div>
            <button data-action="click->claude-code#copyToClipboard" data-code-type="tests"
                    class="inline-flex items-center px-3 py-1 text-sm text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Copy
            </button>
          </div>
          <div class="p-6">
            <pre id="tests-code" class="text-sm bg-gray-50 dark:bg-coffee-900 p-4 rounded-lg overflow-x-auto font-mono text-gray-800 dark:text-gray-200"></pre>
          </div>
        </div>

        <!-- Documentation Tab -->
        <div id="docs-tab" class="code-tab hidden">
          <div class="px-6 py-4 border-b border-gray-100 dark:border-dark-border flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
              <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">API Documentation</h4>
            </div>
            <button data-action="click->claude-code#copyToClipboard" data-code-type="docs"
                    class="inline-flex items-center px-3 py-1 text-sm text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Copy
            </button>
          </div>
          <div class="p-6">
            <pre id="documentation-code" class="text-sm bg-gray-50 dark:bg-coffee-900 p-4 rounded-lg overflow-x-auto font-mono text-gray-800 dark:text-gray-200 whitespace-pre-wrap"></pre>
          </div>
        </div>

        <!-- Implementation Guide -->
        <div class="mt-6 bg-teal-50 dark:bg-teal-900/20 rounded-xl p-6 border border-teal-200 dark:border-teal-800">
          <h4 class="text-sm font-semibold text-teal-900 dark:text-teal-100 mb-3">Implementation Steps</h4>
          <ol class="list-decimal list-inside space-y-2 text-sm text-teal-800 dark:text-teal-200">
            <li>Create the controller file in <code class="bg-teal-100 dark:bg-teal-800 px-1 rounded">app/controllers/api/v1/</code></li>
            <li>Add the serializer to <code class="bg-teal-100 dark:bg-teal-800 px-1 rounded">app/serializers/</code></li>
            <li>Update your routes in <code class="bg-teal-100 dark:bg-teal-800 px-1 rounded">config/routes.rb</code></li>
            <li>Run the tests to ensure everything works correctly</li>
            <li>Update your API documentation</li>
          </ol>
        </div>
      </div>

      <!-- Generate Another Button -->
      <div class="mt-6 text-center">
        <button onclick="resetBuilder()" class="inline-flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Generate Another API
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Template loading functions
function loadTemplate(type) {
  document.getElementById('initial-state').classList.add('hidden');
  document.getElementById('api-form').classList.remove('hidden');
  
  const templates = {
    rest: {
      resource: 'document_extractions',
      requirements: `I need a document extraction API that:
• Accepts document uploads (PDF, JPG, PNG formats)
• Automatically detects document type (invoice, receipt, contract)
• Extracts key fields based on document type
• Returns structured JSON with all extracted data
• Includes confidence scores for each extracted field
• Supports batch processing of multiple documents
• Provides extraction history and audit trail`
    },
    webhook: {
      resource: 'extraction_webhooks',
      requirements: `I need a webhook system that:
• Sends notifications when document extraction completes
• Includes extracted data in webhook payload
• Supports different events (extraction.completed, extraction.failed, review.required)
• Validates webhook signatures for security
• Includes retry logic with exponential backoff
• Allows filtering events by document type or status
• Provides webhook delivery logs`
    },
    transformer: {
      resource: 'document_transformers',
      requirements: `I need a document transformation API that:
• Converts extracted data to different formats (CSV, XML, EDI)
• Maps Docutiz fields to client's system fields
• Validates transformed data against business rules
• Handles data enrichment (tax calculations, lookups)
• Supports custom transformation templates
• Provides preview before final transformation
• Includes error handling and validation reports`
    },
    custom: {
      resource: '',
      requirements: ''
    }
  };
  
  const template = templates[type];
  document.getElementById('resource-name').value = template.resource;
  document.getElementById('api-requirements').value = template.requirements;
}

function showCustomForm() {
  document.getElementById('initial-state').classList.add('hidden');
  document.getElementById('api-form').classList.remove('hidden');
}

function hideApiForm() {
  document.getElementById('api-form').classList.add('hidden');
  document.getElementById('initial-state').classList.remove('hidden');
}

function showTab(tabName) {
  // Hide all tabs
  document.querySelectorAll('.code-tab').forEach(tab => {
    tab.classList.add('hidden');
  });
  
  // Remove active state from all buttons
  document.querySelectorAll('.tab-button').forEach(btn => {
    btn.classList.remove('bg-teal-50', 'dark:bg-teal-900/20', 'text-teal-700', 'dark:text-teal-300');
    btn.classList.add('text-gray-600', 'dark:text-gray-400');
  });
  
  // Show selected tab
  document.getElementById(`${tabName}-tab`).classList.remove('hidden');
  
  // Add active state to selected button
  const activeBtn = document.getElementById(`tab-${tabName}`);
  activeBtn.classList.remove('text-gray-600', 'dark:text-gray-400');
  activeBtn.classList.add('bg-teal-50', 'dark:bg-teal-900/20', 'text-teal-700', 'dark:text-teal-300');
}

function resetBuilder() {
  // Clear form
  document.getElementById('resource-name').value = '';
  document.getElementById('api-requirements').value = '';
  
  // Reset checkboxes
  document.getElementById('include-auth').checked = true;
  document.getElementById('include-tests').checked = true;
  document.getElementById('include-docs').checked = true;
  document.getElementById('include-webhooks').checked = false;
  document.getElementById('include-versioning').checked = false;
  
  // Hide results and show initial state
  document.querySelector('[data-claude-code-target="result"]').classList.add('hidden');
  document.getElementById('api-form').classList.add('hidden');
  document.getElementById('initial-state').classList.remove('hidden');
  
  // Reset to first tab
  showTab('controller');
}

// Fallback API generation if Stimulus isn't working
document.addEventListener('DOMContentLoaded', function() {
  console.log('Setting up fallback handler');
  
  const generateBtn = document.getElementById('generate-api-btn');
  if (generateBtn) {
    generateBtn.addEventListener('click', async function(e) {
      console.log('Fallback handler triggered');
      e.preventDefault();
      
      const resourceName = document.getElementById('resource-name').value;
      const requirements = document.getElementById('api-requirements').value;
      
      if (!resourceName || !requirements) {
        alert('Please provide both resource name and requirements');
        return;
      }
      
      // Get feature checkboxes
      const features = {
        includeAuth: document.getElementById('include-auth').checked,
        includeTests: document.getElementById('include-tests').checked,
        includeDocs: document.getElementById('include-docs').checked,
        includeWebhooks: document.getElementById('include-webhooks').checked,
        includeVersioning: document.getElementById('include-versioning').checked
      };
      
      // Hide form and show loading
      document.getElementById('api-form').classList.add('hidden');
      document.querySelector('[data-claude-code-target="loading"]').classList.remove('hidden');
      
      try {
        const response = await fetch('/claude_code/generate_api', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
          },
          body: JSON.stringify({
            resource_name: resourceName,
            requirements: requirements,
            features: features
          })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
          // Hide loading and show results
          document.querySelector('[data-claude-code-target="loading"]').classList.add('hidden');
          document.querySelector('[data-claude-code-target="result"]').classList.remove('hidden');
          
          // Populate code blocks
          document.getElementById('controller-code').textContent = data.generated_code.controller;
          document.getElementById('serializer-code').textContent = data.generated_code.serializer;
          document.getElementById('tests-code').textContent = data.generated_code.tests;
          document.getElementById('documentation-code').textContent = data.generated_code.documentation;
          
          // Update file paths
          const resourcePath = resourceName.toLowerCase();
          const serializerPath = resourcePath.endsWith('s')
            ? resourcePath.slice(0, -1)
            : resourcePath;
          
          document.querySelector('#controller-tab h4').textContent =
            `app/controllers/api/v1/${resourcePath}_controller.rb`;
          document.querySelector('#serializer-tab h4').textContent =
            `app/serializers/${serializerPath}_serializer.rb`;
          document.querySelector('#tests-tab h4').textContent =
            `spec/requests/api/v1/${resourcePath}_spec.rb`;
        } else {
          document.querySelector('[data-claude-code-target="loading"]').classList.add('hidden');
          alert('Error: ' + data.message);
          document.getElementById('api-form').classList.remove('hidden');
        }
      } catch (error) {
        document.querySelector('[data-claude-code-target="loading"]').classList.add('hidden');
        alert('Failed to generate API: ' + error.message);
        document.getElementById('api-form').classList.remove('hidden');
      }
    });
  }
});
</script>