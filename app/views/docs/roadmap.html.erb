<div class="min-h-screen bg-gray-50 dark:bg-dark-bg">
  <!-- Simple Navigation Header -->
  <nav class="bg-white dark:bg-dark-surface border-b border-gray-200 dark:border-dark-border">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center space-x-8">
          <!-- Logo -->
          <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <span class="font-semibold text-xl text-gray-900 dark:text-gray-100">Docutiz</span>
          </a>
          
          <!-- Navigation Links -->
          <div class="hidden md:flex items-center space-x-6">
            <a href="/api-docs" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">API Docs</a>
            <a href="/changelog" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Changelog</a>
            <a href="/roadmap" class="text-teal-600 dark:text-teal-400 font-medium">Roadmap</a>
          </div>
        </div>
        
        <!-- Right side -->
        <div class="flex items-center space-x-4">
          <% if user_signed_in? %>
            <a href="/dashboard" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Dashboard</a>
          <% else %>
            <%= link_to "Sign In", new_user_session_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100" %>
            <%= link_to "Get Started", new_user_registration_path, class: "px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 text-sm font-medium" %>
          <% end %>
        </div>
      </div>
    </div>
  </nav>
  
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="mb-12">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100">Product Roadmap</h1>
      <p class="mt-4 text-lg text-gray-600 dark:text-gray-400">
        See what we're building next and help shape the future of Docutiz
      </p>
    </div>

    <!-- Timeline -->
    <div class="space-y-12">
      <!-- Q1 2025 -->
      <div>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Q1 2025</h2>
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <!-- Feature Card -->
          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                In Progress
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Real-time Collaboration</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Multiple users can work on document extraction templates simultaneously with live updates.
            </p>
          </div>

          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Planned
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Desktop Application</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Native desktop app for Windows and macOS with offline processing capabilities.
            </p>
          </div>

          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Planned
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Advanced Analytics</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Detailed insights into extraction accuracy, processing times, and usage patterns.
            </p>
          </div>
        </div>
      </div>

      <!-- Q2 2025 -->
      <div>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Q2 2025</h2>
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Researching
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Computer Vision OCR</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Enhanced OCR capabilities for handwritten documents and low-quality scans.
            </p>
          </div>

          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Researching
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Enterprise SSO</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              SAML and OIDC support for seamless enterprise authentication.
            </p>
          </div>

          <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Researching
              </span>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Multi-language Support</h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Extract data from documents in 20+ languages with automatic translation.
            </p>
          </div>
        </div>
      </div>

      <!-- Future -->
      <div>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Future Considerations</h2>
        <div class="bg-white dark:bg-dark-surface shadow rounded-lg p-6">
          <ul class="space-y-3">
            <li class="flex items-start">
              <svg class="h-5 w-5 text-teal-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-gray-900 dark:text-gray-100 font-medium">Mobile Application</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">iOS and Android apps for document capture and extraction on the go</p>
              </div>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 text-teal-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-gray-900 dark:text-gray-100 font-medium">Blockchain Verification</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">Immutable audit trails for document processing and extraction results</p>
              </div>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 text-teal-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-gray-900 dark:text-gray-100 font-medium">AI Model Marketplace</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">Community-contributed extraction models for specialized document types</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Feedback -->
    <div class="bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-6 mt-12">
      <h3 class="text-lg font-medium text-teal-900 dark:text-teal-100 mb-2">Have a Feature Request?</h3>
      <p class="text-teal-700 dark:text-teal-300">
        We'd love to hear your ideas! Share your feedback and vote on features in our
        <a href="#" class="underline">community forum</a>.
      </p>
    </div>
  </div>
</div>