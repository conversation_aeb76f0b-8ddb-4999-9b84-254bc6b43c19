<div class="min-h-screen bg-gray-50 dark:bg-dark-bg">
  <!-- Simple Navigation Header -->
  <nav class="bg-white dark:bg-dark-surface border-b border-gray-200 dark:border-dark-border">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center space-x-8">
          <!-- Logo -->
          <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <span class="font-semibold text-xl text-gray-900 dark:text-gray-100">Docutiz</span>
          </a>
          
          <!-- Navigation Links -->
          <div class="hidden md:flex items-center space-x-6">
            <a href="/api-docs" class="text-teal-600 dark:text-teal-400 font-medium">API Docs</a>
            <a href="/changelog" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Changelog</a>
            <a href="/roadmap" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Roadmap</a>
          </div>
        </div>
        
        <!-- Right side -->
        <div class="flex items-center space-x-4">
          <% if user_signed_in? %>
            <a href="/dashboard" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Dashboard</a>
          <% else %>
            <%= link_to "Sign In", new_user_session_path, class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100" %>
            <%= link_to "Get Started", new_user_registration_path, class: "px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 text-sm font-medium" %>
          <% end %>
        </div>
      </div>
    </div>
  </nav>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Header -->
    <div class="mb-12">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100">API Documentation</h1>
      <p class="mt-4 text-lg text-gray-600 dark:text-gray-400">
        Integrate Docutiz's powerful document extraction capabilities into your applications
      </p>
    </div>

    <!-- Getting Started -->
    <div class="bg-white dark:bg-dark-surface shadow rounded-lg mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Getting Started</h2>
      </div>
      <div class="p-6 space-y-4">
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Authentication</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            All API requests require authentication using your API key. Include your API key in the Authorization header:
          </p>
          <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto"><code>Authorization: Bearer YOUR_API_KEY</code></pre>
        </div>
        
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Base URL</h3>
          <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto"><code>https://your-tenant.docutiz.com/api/v1</code></pre>
        </div>
        
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Rate Limits</h3>
          <p class="text-gray-600 dark:text-gray-400">
            API requests are limited to 100 requests per minute. Rate limit information is included in response headers:
          </p>
          <ul class="list-disc list-inside mt-2 text-gray-600 dark:text-gray-400">
            <li><code class="bg-gray-100 dark:bg-dark-surface px-1 rounded">X-RateLimit-Limit</code>: Request limit per minute</li>
            <li><code class="bg-gray-100 dark:bg-dark-surface px-1 rounded">X-RateLimit-Remaining</code>: Remaining requests</li>
            <li><code class="bg-gray-100 dark:bg-dark-surface px-1 rounded">X-RateLimit-Reset</code>: Time when limit resets</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Endpoints -->
    <div class="space-y-8">
      <!-- Create Document Extraction -->
      <div class="bg-white dark:bg-dark-surface shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Create Document Extraction</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              POST
            </span>
          </div>
        </div>
        <div class="p-6">
          <div class="mb-4">
            <code class="text-sm bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded">/document_extractions</code>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Request Body</h4>
            <%= render 'shared/code_block', code: '{
  "document": {
    "extraction_template_id": "uuid",  // Required: Extraction template ID
    "file": "<multipart-file>",       // Required: Document file (multipart/form-data)
    "name": "Invoice January",         // Optional: Custom name for document
    "metadata": {                       // Optional: Custom metadata
      "customer_id": "123",
      "order_id": "456"
    }
  }
}' %>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Response</h4>
            <%= render 'shared/code_block', code: '{
  "id": "uuid",
  "status": "processing",
  "message": "Document uploaded successfully. Extraction in progress.",
  "polling_url": "https://your-tenant.docutiz.com/api/v1/document_extractions/uuid/status"
}' %>
          </div>
        </div>
      </div>

      <!-- Get Extraction Status -->
      <div class="bg-white dark:bg-dark-surface shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Get Extraction Status</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              GET
            </span>
          </div>
        </div>
        <div class="p-6">
          <div class="mb-4">
            <code class="text-sm bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded">/document_extractions/:id/status</code>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Response</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto text-sm"><code>{
  "id": "uuid",
  "status": "completed",
  "progress": 100,
  "extracted_data": {
    "invoice_number": "INV-2024-001",
    "date": "2024-01-15",
    "total_amount": 1250.00,
    "vendor_name": "Acme Corp",
    "line_items": [...]
  },
  "confidence_scores": {
    "invoice_number": 0.98,
    "date": 0.95,
    "total_amount": 0.99
  },
  "processing_time": 3.2,
  "completed_at": "2024-01-15T10:30:03Z"
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Get Extraction Result -->
      <div class="bg-white dark:bg-dark-surface shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Get Extraction Result</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              GET
            </span>
          </div>
        </div>
        <div class="p-6">
          <div class="mb-4">
            <code class="text-sm bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded">/document_extractions/:id</code>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Response</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto text-sm"><code>{
  "id": "uuid",
  "document": {
    "id": "uuid",
    "name": "invoice.pdf",
    "size": 125000,
    "content_type": "application/pdf"
  },
  "template": {
    "id": "uuid",
    "name": "Invoice Extraction",
    "fields": [...]
  },
  "status": "completed",
  "extracted_data": {...},
  "confidence_scores": {...},
  "metadata": {...},
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:30:03Z"
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Batch Create -->
      <div class="bg-white dark:bg-dark-surface shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Batch Document Extraction</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              POST
            </span>
          </div>
        </div>
        <div class="p-6">
          <div class="mb-4">
            <code class="text-sm bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded">/document_extractions/batch</code>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Request Body</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto text-sm"><code>{
  "template_id": "uuid",
  "documents": [
    {
      "file": "<multipart-file>",
      "name": "Invoice 1",
      "metadata": {...}
    },
    {
      "file": "<multipart-file>",
      "name": "Invoice 2",
      "metadata": {...}
    }
  ]
}</code></pre>
          </div>
          
          <div class="mb-6">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Response</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto text-sm"><code>{
  "batch_id": "uuid",
  "status": "processing",
  "total_documents": 2,
  "documents": [
    {
      "id": "uuid1",
      "status": "processing"
    },
    {
      "id": "uuid2", 
      "status": "processing"
    }
  ]
}</code></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Code Examples -->
    <div class="bg-white dark:bg-dark-surface shadow rounded-lg mt-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Code Examples</h2>
      </div>
      <div class="p-6 space-y-6">
        <!-- cURL Example -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Upload Document with cURL</h3>
          <%= render 'shared/code_block', code: 'curl -X POST https://your-tenant.docutiz.com/api/v1/document_extractions \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Accept: application/json" \
  -F "document[extraction_template_id]=template-uuid" \
  -F "document[file]=@/path/to/invoice.pdf" \
  -F "document[name]=January Invoice" \
  -F "document[metadata][customer_id]=123"' %>
        </div>

        <!-- JavaScript Example -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">JavaScript/Node.js Example</h3>
          <%= render 'shared/code_block', code: "const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');

const form = new FormData();
form.append('document[extraction_template_id]', 'template-uuid');
form.append('document[file]', fs.createReadStream('/path/to/invoice.pdf'));
form.append('document[name]', 'January Invoice');
form.append('document[metadata][customer_id]', '123');

const response = await axios.post(
  'https://your-tenant.docutiz.com/api/v1/document_extractions',
  form,
  {
    headers: {
      ...form.getHeaders(),
      'Authorization': 'Bearer YOUR_API_TOKEN',
      'Accept': 'application/json'
    }
  }
);

console.log(response.data);" %>
        </div>

        <!-- Python Example -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Python Example</h3>
          <%= render 'shared/code_block', code: "import requests

url = 'https://your-tenant.docutiz.com/api/v1/document_extractions'
headers = {
    'Authorization': 'Bearer YOUR_API_TOKEN',
    'Accept': 'application/json'
}

files = {
    'document[file]': open('/path/to/invoice.pdf', 'rb')
}

data = {
    'document[extraction_template_id]': 'template-uuid',
    'document[name]': 'January Invoice',
    'document[metadata][customer_id]': '123'
}

response = requests.post(url, headers=headers, files=files, data=data)
print(response.json())" %>
        </div>

        <!-- Ruby Example -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Ruby Example</h3>
          <%= render 'shared/code_block', code: "require 'net/http'
require 'uri'

uri = URI('https://your-tenant.docutiz.com/api/v1/document_extractions')
request = Net::HTTP::Post.new(uri)
request['Authorization'] = 'Bearer YOUR_API_TOKEN'
request['Accept'] = 'application/json'

form_data = [
  ['document[extraction_template_id]', 'template-uuid'],
  ['document[name]', 'January Invoice'],
  ['document[metadata][customer_id]', '123'],
  ['document[file]', File.open('/path/to/invoice.pdf')]
]

request.set_form(form_data, 'multipart/form-data')
response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
  http.request(request)
end

puts response.body" %>
        </div>
      </div>
    </div>

    <!-- Error Handling -->
    <div class="bg-white dark:bg-dark-surface shadow rounded-lg mt-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Error Handling</h2>
      </div>
      <div class="p-6">
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          The API uses standard HTTP status codes and returns JSON error responses:
        </p>
        
        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-gray-900 dark:text-gray-100">400 Bad Request</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 mt-2 text-sm"><code>{
  "error": "validation_error",
  "message": "Template ID is required",
  "details": {
    "template_id": ["can't be blank"]
  }
}</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 dark:text-gray-100">401 Unauthorized</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 mt-2 text-sm"><code>{
  "error": "unauthorized",
  "message": "Invalid or missing API key"
}</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 dark:text-gray-100">429 Too Many Requests</h4>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 mt-2 text-sm"><code>{
  "error": "rate_limit_exceeded",
  "message": "Rate limit exceeded. Please retry after 60 seconds",
  "retry_after": 60
}</code></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- SDKs -->
    <div class="bg-white dark:bg-dark-surface shadow rounded-lg mt-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">SDKs & Examples</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Python</h3>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 text-sm overflow-x-auto"><code>import requests

api_key = "YOUR_API_KEY"
base_url = "https://your-tenant.docutiz.com/api/v1"

# Create extraction
with open("invoice.pdf", "rb") as f:
    response = requests.post(
        f"{base_url}/document_extractions",
        headers={"Authorization": f"Bearer {api_key}"},
        files={"document[file]": f},
        data={
            "document[extraction_template_id]": "uuid",
            "document[name]": "Invoice January"
        }
    )

extraction = response.json()
print(f"Extraction ID: {extraction['id']}")</code></pre>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-2">JavaScript</h3>
            <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 text-sm overflow-x-auto"><code>const apiKey = 'YOUR_API_KEY';
const baseUrl = 'https://your-tenant.docutiz.com/api/v1';

// Create extraction
const formData = new FormData();
formData.append('document[file]', fileInput.files[0]);
formData.append('document[extraction_template_id]', 'uuid');
formData.append('document[name]', 'Invoice January');

const response = await fetch(`${baseUrl}/document_extractions`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`
  },
  body: formData
});

const extraction = await response.json();
console.log(`Extraction ID: ${extraction.id}`);</code></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Webhooks -->
    <div class="bg-white dark:bg-dark-surface shadow rounded-lg mt-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-dark-border">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Webhooks</h2>
      </div>
      <div class="p-6">
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          When you provide a webhook_url, we'll send a POST request when extraction is complete:
        </p>
        
        <pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto text-sm"><code>{
  "event": "extraction.completed",
  "timestamp": "2024-01-15T10:30:03Z",
  "data": {
    "id": "uuid",
    "status": "completed",
    "document": {...},
    "extracted_data": {...},
    "confidence_scores": {...},
    "metadata": {...}
  }
}</code></pre>
        
        <div class="mt-4">
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Webhook Security</h4>
          <p class="text-gray-600 dark:text-gray-400">
            All webhooks include an HMAC-SHA256 signature in the <code class="bg-gray-100 dark:bg-dark-surface px-1 rounded">X-Webhook-Signature</code> header for verification.
          </p>
          <div class="mt-4">
            <%= link_to "View webhook verification guide →", "#webhook-verification", 
                class: "text-blue-600 dark:text-blue-400 hover:underline text-sm" %>
          </div>
        </div>
      </div>
    </div>

    <!-- Webhook Verification Guide -->
    <div id="webhook-verification" class="bg-white dark:bg-dark-primary rounded-lg shadow-md p-6 mb-8">
      <div class="flex items-center gap-2 mb-4">
        <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Webhook Verification</h2>
      </div>
      <%= render 'docs/webhook_verification' %>
    </div>

    <!-- Support -->
    <div class="bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-6 mt-8">
      <h3 class="text-lg font-medium text-teal-900 dark:text-teal-100 mb-2">Need Help?</h3>
      <p class="text-teal-700 dark:text-teal-300">
        If you have questions or need assistance with the API, please contact our support team at 
        <a href="mailto:<EMAIL>" class="underline"><EMAIL></a> or visit our 
        <a href="https://community.docutiz.com" class="underline">developer forum</a>.
      </p>
    </div>
  </div>
</div>