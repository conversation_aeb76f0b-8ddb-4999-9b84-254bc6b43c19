<div class="px-4 py-8 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Document Queue Management</h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Monitor and manage document processing queues</p>
      </div>
      <div>
        <%= link_to clear_stale_document_queues_path, method: :post, class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-coffee-700 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-200 bg-white dark:bg-coffee-800 hover:bg-gray-50 dark:hover:bg-coffee-700" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Clear Stale Documents
        <% end %>
      </div>
    </div>
  </div>

  <!-- Queue Statistics -->
  <div class="mb-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Pending</dt>
              <dd class="text-lg font-semibold text-gray-900 dark:text-gray-100"><%= @queue_stats[:total_pending] || 0 %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Processing Rate</dt>
              <dd class="text-lg font-semibold text-gray-900 dark:text-gray-100"><%= (@queue_stats[:processing_rate] || 0).round(1) %>/min</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Avg Wait Time</dt>
              <dd class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                <%= distance_of_time_in_words(@queue_stats[:average_wait_time] || 0) %>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-coffee-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Est. Completion</dt>
              <dd class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                <% if @queue_stats[:estimated_completion] %>
                  <%= @queue_stats[:estimated_completion].strftime("%H:%M") %>
                <% else %>
                  -
                <% end %>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Priority Breakdown -->
  <% if @queue_stats[:by_priority]&.any? %>
    <div class="mb-8 bg-white dark:bg-coffee-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-coffee-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Queue by Priority</h3>
      </div>
      <div class="p-6">
        <div class="flex items-center space-x-8">
          <% @queue_stats[:by_priority].each do |priority, count| %>
            <div class="flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= priority_color(priority.to_s) %>">
                <%= priority.to_s.humanize %>
              </span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400"><%= count %></span>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Processing Documents -->
  <% if @processing_documents.any? %>
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Currently Processing</h2>
      <div class="bg-white dark:bg-coffee-800 shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-coffee-700">
          <thead class="bg-gray-50 dark:bg-coffee-900">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Document</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Started</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Progress</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">ETA</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Model</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-coffee-800 divide-y divide-gray-200 dark:divide-coffee-700">
            <% @processing_documents.each do |document| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= link_to document.name, document, class: "text-sm text-teal-600 hover:text-teal-900" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.user.name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(document.processing_started_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <% progress = document.processing_started_at ? [(Time.current - document.processing_started_at) / document.processing_time_estimate * 100, 100].min : 0 %>
                    <div class="bg-teal-600 h-2 rounded-full" style="width: <%= progress %>%"></div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <% if document.estimated_completion_at %>
                    <%= document.estimated_completion_at.strftime("%H:%M") %>
                    <% if document.overdue? %>
                      <span class="ml-1 text-red-600">(overdue)</span>
                    <% end %>
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.assigned_model&.humanize || "Default" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>

  <!-- Pending Queue -->
  <% if @pending_documents.any? %>
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Pending Queue (Top 20)</h2>
      <div class="bg-white dark:bg-coffee-800 shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-coffee-700">
          <thead class="bg-gray-50 dark:bg-coffee-900">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Priority</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Document</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Template</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Waiting</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Reason</th>
              <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-coffee-800 divide-y divide-gray-200 dark:divide-coffee-700">
            <% @pending_documents.each do |document| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= priority_color(document.priority) %>">
                    <%= document.priority.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= link_to document.name, document, class: "text-sm text-teal-600 hover:text-teal-900" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.extraction_template&.name || "-" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.user.name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(document.created_at) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.priority_reason || "-" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="relative inline-block text-left" data-controller="dropdown">
                    <button type="button" data-action="click->dropdown#toggle" class="text-gray-400 hover:text-gray-600">
                      <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>
                    <div data-dropdown-target="menu" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-coffee-800 ring-1 ring-black ring-opacity-5 z-10">
                      <div class="py-1">
                        <%= link_to "Make Critical", priority_document_queue_path(document, priority: "critical"), method: :patch, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                        <%= link_to "Make Urgent", priority_document_queue_path(document, priority: "urgent"), method: :patch, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                        <%= link_to "Make High", priority_document_queue_path(document, priority: "high"), method: :patch, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                        <%= link_to "Make Normal", priority_document_queue_path(document, priority: "normal"), method: :patch, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-coffee-700" %>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>

  <!-- Recent Failures -->
  <% if @recent_failures.any? %>
    <div>
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Failures</h2>
      <div class="bg-white dark:bg-coffee-800 shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-coffee-700">
          <thead class="bg-gray-50 dark:bg-coffee-900">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Document</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Failed</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Retries</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Error</th>
              <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-coffee-800 divide-y divide-gray-200 dark:divide-coffee-700">
            <% @recent_failures.each do |document| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%= link_to document.name, document, class: "text-sm text-teal-600 hover:text-teal-900" %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(document.updated_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= document.retry_count %>
                </td>
                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="truncate block max-w-xs" title="<%= document.last_error %>">
                    <%= document.last_error&.truncate(50) || "Unknown error" %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <%= link_to "Retry", requeue_document_queue_path(document), method: :post, class: "text-teal-600 hover:text-teal-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>
</div>