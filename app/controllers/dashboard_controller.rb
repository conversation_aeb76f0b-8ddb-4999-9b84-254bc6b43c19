class DashboardController < ApplicationController
  layout 'dashboard'
  before_action :require_tenant!
  before_action :authorize_dashboard!

  def index
    @tenant = Current.tenant
    @user = current_user

    # Parse date range from params or use default
    @date_range = parse_date_range

    # Get comprehensive analytics
    analytics_service = DashboardAnalyticsService.for_tenant(@tenant, @user, @date_range)
    @metrics = analytics_service.dashboard_metrics

    # Legacy stats for backward compatibility
    @stats = @metrics[:overview] || {}

    # Recent activity for sidebar or activity feed
    @recent_activities = @tenant.activities
                                .includes(:user, :trackable)
                                .order(created_at: :desc)
                                .limit(10)

    # Quick actions based on user role
    @quick_actions = build_quick_actions

    # Alerts and notifications
    @alerts = build_dashboard_alerts

    respond_to do |format|
      format.html
      format.json { render json: @metrics }
    end
  end

  def test_theme
    # Test page for theme toggle
  end

  # AJAX endpoint for refreshing specific dashboard sections
  def refresh_section
    section = params[:section]
    @date_range = parse_date_range

    analytics_service = DashboardAnalyticsService.for_tenant(Current.tenant, current_user, @date_range)

    case section
    when 'overview'
      @metrics = { overview: analytics_service.overview_metrics }
    when 'document_processing'
      @metrics = { document_processing: analytics_service.document_processing_metrics }
    when 'user_activity'
      @metrics = { user_activity: analytics_service.user_activity_metrics }
    when 'billing'
      @metrics = { billing: analytics_service.billing_metrics }
    when 'system_performance'
      @metrics = { system_performance: analytics_service.system_performance_metrics }
    else
      @metrics = analytics_service.dashboard_metrics
    end

    render json: @metrics
  end

  private

  def authorize_dashboard!
    dashboard = Dashboard.for_tenant(Current.tenant)
    authorize dashboard, :index?
  end

  def parse_date_range
    if params[:date_range].present?
      case params[:date_range]
      when 'today'
        Time.current.beginning_of_day..Time.current.end_of_day
      when 'yesterday'
        1.day.ago.beginning_of_day..1.day.ago.end_of_day
      when 'this_week'
        Time.current.beginning_of_week..Time.current.end_of_week
      when 'last_week'
        1.week.ago.beginning_of_week..1.week.ago.end_of_week
      when 'this_month'
        Time.current.beginning_of_month..Time.current.end_of_month
      when 'last_month'
        1.month.ago.beginning_of_month..1.month.ago.end_of_month
      when 'last_7_days'
        7.days.ago.beginning_of_day..Time.current.end_of_day
      when 'last_30_days'
        30.days.ago.beginning_of_day..Time.current.end_of_day
      when 'last_90_days'
        90.days.ago.beginning_of_day..Time.current.end_of_day
      when 'custom'
        start_date = Date.parse(params[:start_date]) rescue 30.days.ago.to_date
        end_date = Date.parse(params[:end_date]) rescue Date.current
        start_date.beginning_of_day..end_date.end_of_day
      else
        30.days.ago.beginning_of_day..Time.current.end_of_day
      end
    else
      30.days.ago.beginning_of_day..Time.current.end_of_day
    end
  end

  def build_quick_actions
    actions = []

    # Always available actions
    actions << {
      title: "Upload Document",
      description: "Process a new document",
      url: new_document_path,
      icon: "upload",
      color: "teal"
    }

    actions << {
      title: "Create Template",
      description: "Build extraction template",
      url: new_extraction_template_path,
      icon: "template",
      color: "blue"
    }

    # Admin/Owner actions
    if current_user.can_manage_users?
      actions << {
        title: "Invite Team Member",
        description: "Add someone to your team",
        url: new_team_path,
        icon: "user-plus",
        color: "green"
      }

      actions << {
        title: "Queue Dashboard",
        description: "Monitor processing queue",
        url: document_queues_path,
        icon: "queue",
        color: "yellow"
      }
    end

    # Owner actions
    if current_user.can_manage_billing?
      actions << {
        title: "Billing Settings",
        description: "Manage subscription",
        url: billing_settings_path,
        icon: "credit-card",
        color: "purple"
      }
    end

    actions
  end

  def build_dashboard_alerts
    alerts = []

    # Trial expiration warning
    if @tenant.trial? && @tenant.trial_days_remaining <= 7
      alerts << {
        type: "warning",
        title: "Trial Ending Soon",
        message: "Your trial expires in #{@tenant.trial_days_remaining} days. Upgrade to keep your data.",
        action_text: "Upgrade Now",
        action_url: billing_settings_path
      }
    end

    # Usage limit warnings
    usage_stats = @metrics[:billing][:usage_vs_limits] if @metrics[:billing]
    if usage_stats
      if usage_stats[:documents_used] >= usage_stats[:documents_limit] * 0.9
        alerts << {
          type: "warning",
          title: "Document Limit Approaching",
          message: "You've used #{usage_stats[:documents_used]} of #{usage_stats[:documents_limit]} documents this month.",
          action_text: "Upgrade Plan",
          action_url: billing_settings_path
        }
      end
    end

    # Failed documents alert
    failed_count = @tenant.documents.failed.count
    if failed_count > 0
      alerts << {
        type: "error",
        title: "Failed Documents",
        message: "#{failed_count} documents failed processing and need attention.",
        action_text: "View Queue",
        action_url: document_queues_path
      }
    end

    # High error rate alert
    if @metrics[:system_performance][:error_rates][:error_rate] > 10
      alerts << {
        type: "error",
        title: "High Error Rate",
        message: "Document processing error rate is above normal. Check system status.",
        action_text: "View Details",
        action_url: document_queues_path
      }
    end

    alerts
  end
end
