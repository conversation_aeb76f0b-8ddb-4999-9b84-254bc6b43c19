class BillingController < ApplicationController
  before_action :authenticate_user!
  before_action :authorize_billing_management!
  
  def show
    @subscription = current_tenant.subscription
    @payment_methods = current_tenant.payment_processor&.payment_methods || []
    @invoices = current_tenant.payment_processor&.charges&.where(status: "succeeded")&.order(created_at: :desc) || []
    @upcoming_invoice = current_tenant.payment_processor&.upcoming_invoice
  end
  
  def portal
    session = current_tenant.payment_processor.billing_portal(
      return_url: billing_settings_url(subdomain: current_tenant.subdomain)
    )
    redirect_to session.url, allow_other_host: true
  end
  
  def subscribe
    plan_id = params[:plan_id]
    payment_method_id = params[:payment_method_id]
    
    begin
      current_tenant.subscribe_to_plan(plan_id, payment_method_id)
      redirect_to billing_settings_path, notice: "Successfully subscribed to plan!"
    rescue => e
      redirect_to billing_settings_path, alert: "Error subscribing: #{e.message}"
    end
  end
  
  def change_plan
    new_plan_id = params[:plan_id]
    
    begin
      if current_tenant.change_plan(new_plan_id)
        redirect_to billing_settings_path, notice: "Plan changed successfully!"
      else
        redirect_to billing_settings_path, alert: "Unable to change plan."
      end
    rescue => e
      redirect_to billing_settings_path, alert: "Error changing plan: #{e.message}"
    end
  end
  
  def cancel
    at_period_end = params[:immediately] != "true"
    
    if current_tenant.cancel_subscription(at_period_end: at_period_end)
      notice = at_period_end ? "Subscription will be canceled at the end of the billing period." : "Subscription canceled immediately."
      redirect_to billing_settings_path, notice: notice
    else
      redirect_to billing_settings_path, alert: "Unable to cancel subscription."
    end
  end
  
  def resume
    if current_tenant.resume_subscription
      redirect_to billing_settings_path, notice: "Subscription resumed successfully!"
    else
      redirect_to billing_settings_path, alert: "Unable to resume subscription."
    end
  end
  
  private
  
  def authorize_billing_management!
    unless current_user.can_manage_billing?
      redirect_to dashboard_path, alert: "You are not authorized to manage billing."
    end
  end
  
  def current_tenant
    @current_tenant ||= current_user.tenant
  end
end