class DashboardExportsController < ApplicationController
  layout 'dashboard'
  before_action :require_tenant!
  before_action :authorize_export!
  
  def show
    # Show export options and form
    @date_ranges = date_range_options
    @export_formats = DashboardExportService::EXPORT_FORMATS
  end
  
  def create
    @export_service = DashboardExportService.new(
      Current.tenant,
      current_user,
      parse_date_range
    )
    
    format = params[:format]&.downcase
    
    unless DashboardExportService::EXPORT_FORMATS.include?(format)
      redirect_to dashboard_export_path, alert: "Invalid export format: #{format}"
      return
    end
    
    begin
      case format
      when 'csv'
        export_csv
      when 'excel'
        export_excel
      when 'pdf'
        export_pdf
      when 'json'
        export_json
      end
    rescue => e
      Rails.logger.error "Export failed: #{e.message}"
      redirect_to dashboard_export_path, alert: "Export failed: #{e.message}"
    end
  end
  
  # Background export for large datasets
  def create_async
    format = params[:format]&.downcase
    date_range = parse_date_range
    
    unless DashboardExportService::EXPORT_FORMATS.include?(format)
      render json: { error: "Invalid export format: #{format}" }, status: :bad_request
      return
    end
    
    # Queue background job for export
    job = DashboardExportJob.perform_later(
      Current.tenant.id,
      current_user.id,
      format,
      date_range
    )
    
    render json: {
      message: "Export queued successfully",
      job_id: job.job_id,
      estimated_completion: 5.minutes.from_now.iso8601
    }
  end
  
  # Check status of background export
  def status
    job_id = params[:job_id]
    
    # In a real implementation, you'd check the job status
    # For now, we'll return a simple response
    render json: {
      status: 'completed',
      download_url: dashboard_export_download_path(job_id: job_id)
    }
  end
  
  # Download completed export
  def download
    job_id = params[:job_id]
    
    # In a real implementation, you'd retrieve the file from storage
    # For now, we'll generate it on-demand
    @export_service = DashboardExportService.new(
      Current.tenant,
      current_user,
      parse_date_range
    )
    
    format = params[:format] || 'csv'
    export_data = @export_service.export(format)
    filename = @export_service.filename(format)
    
    send_data export_data, 
              filename: filename,
              type: content_type_for_format(format),
              disposition: 'attachment'
  end
  
  private
  
  def authorize_export!
    dashboard = Dashboard.for_tenant(Current.tenant)
    authorize dashboard, :export_data?
  end
  
  def export_csv
    csv_data = @export_service.export_to_csv
    filename = @export_service.filename('csv')
    
    send_data csv_data,
              filename: filename,
              type: 'text/csv',
              disposition: 'attachment'
  end
  
  def export_excel
    excel_data = @export_service.export_to_excel
    filename = @export_service.filename('xlsx')
    
    send_data excel_data,
              filename: filename,
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              disposition: 'attachment'
  end
  
  def export_pdf
    pdf_data = @export_service.export_to_pdf
    filename = @export_service.filename('pdf')
    
    send_data pdf_data,
              filename: filename,
              type: 'application/pdf',
              disposition: 'attachment'
  end
  
  def export_json
    json_data = @export_service.export_to_json
    filename = @export_service.filename('json')
    
    send_data json_data,
              filename: filename,
              type: 'application/json',
              disposition: 'attachment'
  end
  
  def parse_date_range
    return default_date_range unless params[:date_range].present?
    
    case params[:date_range]
    when 'today'
      { start_date: Date.current, end_date: Date.current }
    when 'yesterday'
      { start_date: 1.day.ago.to_date, end_date: 1.day.ago.to_date }
    when 'last_7_days'
      { start_date: 7.days.ago.to_date, end_date: Date.current }
    when 'last_30_days'
      { start_date: 30.days.ago.to_date, end_date: Date.current }
    when 'last_90_days'
      { start_date: 90.days.ago.to_date, end_date: Date.current }
    when 'this_week'
      { start_date: Date.current.beginning_of_week, end_date: Date.current.end_of_week }
    when 'last_week'
      { start_date: 1.week.ago.beginning_of_week, end_date: 1.week.ago.end_of_week }
    when 'this_month'
      { start_date: Date.current.beginning_of_month, end_date: Date.current.end_of_month }
    when 'last_month'
      { start_date: 1.month.ago.beginning_of_month, end_date: 1.month.ago.end_of_month }
    when 'custom'
      {
        start_date: Date.parse(params[:start_date]),
        end_date: Date.parse(params[:end_date])
      }
    else
      default_date_range
    end
  rescue Date::Error
    default_date_range
  end
  
  def default_date_range
    {
      start_date: 30.days.ago.to_date,
      end_date: Date.current
    }
  end
  
  def date_range_options
    [
      ['Today', 'today'],
      ['Yesterday', 'yesterday'],
      ['Last 7 days', 'last_7_days'],
      ['Last 30 days', 'last_30_days'],
      ['Last 90 days', 'last_90_days'],
      ['This week', 'this_week'],
      ['Last week', 'last_week'],
      ['This month', 'this_month'],
      ['Last month', 'last_month'],
      ['Custom range', 'custom']
    ]
  end
  
  def content_type_for_format(format)
    case format.to_s
    when 'csv'
      'text/csv'
    when 'excel', 'xlsx'
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    when 'pdf'
      'application/pdf'
    when 'json'
      'application/json'
    else
      'application/octet-stream'
    end
  end
end
