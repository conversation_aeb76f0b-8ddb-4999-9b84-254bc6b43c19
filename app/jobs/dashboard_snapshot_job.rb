# Background job for generating dashboard snapshots
class DashboardSnapshotJob < ApplicationJob
  queue_as :dashboard_analytics
  
  def perform(tenant_id, snapshot_type = 'daily', date = nil)
    tenant = Tenant.find(tenant_id)
    date ||= Date.current
    
    case snapshot_type
    when 'daily'
      create_daily_snapshot(tenant, date)
    when 'weekly'
      create_weekly_snapshot(tenant, date.beginning_of_week)
    when 'monthly'
      create_monthly_snapshot(tenant, date.beginning_of_month)
    when 'all'
      create_all_snapshots(tenant, date)
    else
      raise ArgumentError, "Invalid snapshot type: #{snapshot_type}"
    end
    
    Rails.logger.info "Dashboard snapshot created for tenant #{tenant.name} (#{snapshot_type})"
  end
  
  private
  
  def create_daily_snapshot(tenant, date)
    DashboardSnapshot.create_daily_snapshot(tenant, date)
    
    # Also record individual metrics for detailed tracking
    record_daily_metrics(tenant, date)
  end
  
  def create_weekly_snapshot(tenant, week_start)
    DashboardSnapshot.create_weekly_snapshot(tenant, week_start)
  end
  
  def create_monthly_snapshot(tenant, month_start)
    DashboardSnapshot.create_monthly_snapshot(tenant, month_start)
  end
  
  def create_all_snapshots(tenant, date)
    create_daily_snapshot(tenant, date)
    
    # Create weekly snapshot if it's Monday or first run
    if date.monday? || should_create_weekly_snapshot?(tenant, date)
      create_weekly_snapshot(tenant, date.beginning_of_week)
    end
    
    # Create monthly snapshot if it's first day of month or first run
    if date.day == 1 || should_create_monthly_snapshot?(tenant, date)
      create_monthly_snapshot(tenant, date.beginning_of_month)
    end
  end
  
  def record_daily_metrics(tenant, date)
    start_time = date.beginning_of_day
    end_time = date.end_of_day
    
    # Document metrics
    total_docs = tenant.documents.where(created_at: start_time..end_time).count
    DashboardMetric.record_metric(tenant, 'document_count', total_docs, recorded_at: end_time)
    
    # Success rate
    success_rate = calculate_success_rate(tenant, start_time, end_time)
    DashboardMetric.record_metric(tenant, 'success_rate', success_rate, recorded_at: end_time)
    
    # Processing time
    avg_processing_time = calculate_avg_processing_time(tenant, start_time, end_time)
    DashboardMetric.record_metric(tenant, 'processing_time', avg_processing_time, recorded_at: end_time)
    
    # User activity
    active_users = tenant.users.where(last_sign_in_at: start_time..end_time).count
    DashboardMetric.record_metric(tenant, 'user_activity', active_users, recorded_at: end_time)
    
    # Revenue (if billing data available)
    if tenant.respond_to?(:pay_customers)
      daily_revenue = calculate_daily_revenue(tenant, start_time, end_time)
      DashboardMetric.record_metric(tenant, 'revenue', daily_revenue, recorded_at: end_time)
    end
    
    # API calls (if API usage tracking available)
    if tenant.respond_to?(:api_requests)
      api_calls = tenant.api_requests.where(created_at: start_time..end_time).count
      DashboardMetric.record_metric(tenant, 'api_calls', api_calls, recorded_at: end_time)
    end
    
    # Error rate
    error_rate = calculate_error_rate(tenant, start_time, end_time)
    DashboardMetric.record_metric(tenant, 'error_rate', error_rate, recorded_at: end_time)
  end
  
  def should_create_weekly_snapshot?(tenant, date)
    !DashboardSnapshot.exists?(
      tenant: tenant,
      snapshot_type: 'weekly',
      snapshot_date: date.beginning_of_week
    )
  end
  
  def should_create_monthly_snapshot?(tenant, date)
    !DashboardSnapshot.exists?(
      tenant: tenant,
      snapshot_type: 'monthly',
      snapshot_date: date.beginning_of_month
    )
  end
  
  def calculate_success_rate(tenant, start_time, end_time)
    total = tenant.documents.where(created_at: start_time..end_time).count
    return 0 if total.zero?
    
    successful = tenant.documents.where(status: 'completed', created_at: start_time..end_time).count
    (successful.to_f / total * 100).round(2)
  end
  
  def calculate_avg_processing_time(tenant, start_time, end_time)
    completed_docs = tenant.documents
                           .where(status: 'completed', updated_at: start_time..end_time)
                           .where.not(processing_started_at: nil)
    
    return 0 if completed_docs.empty?
    
    total_time = completed_docs.sum do |doc|
      (doc.updated_at - doc.processing_started_at).to_f
    end
    
    (total_time / completed_docs.count).round(2)
  end
  
  def calculate_daily_revenue(tenant, start_time, end_time)
    # Calculate revenue from Pay gem charges
    return 0 unless tenant.respond_to?(:pay_customers)
    
    tenant.pay_customers.sum do |customer|
      customer.charges.where(created_at: start_time..end_time).sum(:amount) / 100.0
    end
  end
  
  def calculate_error_rate(tenant, start_time, end_time)
    total = tenant.documents.where(created_at: start_time..end_time).count
    return 0 if total.zero?
    
    failed = tenant.documents.where(status: 'failed', created_at: start_time..end_time).count
    (failed.to_f / total * 100).round(2)
  end
end
