/* Premium Dashboard Styles */

/* ===== DASHBOARD LAYOUT ===== */
.dashboard-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.dark .dashboard-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* ===== PREMIUM CARDS ===== */
.premium-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.premium-card:hover {
  @apply shadow-lg border-gray-200 dark:border-gray-600;
  transform: translateY(-2px);
}

.premium-card:hover::before {
  opacity: 1;
}

.dark .premium-card {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.3);
}

/* ===== KPI CARDS ===== */
.kpi-card {
  @apply premium-card p-6;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  border: 1px solid rgba(255,255,255,0.2);
}

.dark .kpi-card {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(55, 65, 81, 0.7) 100%);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.kpi-icon-container {
  @apply p-3 rounded-xl;
  background: linear-gradient(135deg, var(--icon-bg-start), var(--icon-bg-end));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kpi-card:hover .kpi-icon-container {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Icon color variants */
.kpi-icon-blue {
  --icon-bg-start: #dbeafe;
  --icon-bg-end: #bfdbfe;
}

.dark .kpi-icon-blue {
  --icon-bg-start: #1e3a8a;
  --icon-bg-end: #1d4ed8;
}

.kpi-icon-green {
  --icon-bg-start: #dcfce7;
  --icon-bg-end: #bbf7d0;
}

.dark .kpi-icon-green {
  --icon-bg-start: #14532d;
  --icon-bg-end: #166534;
}

.kpi-icon-purple {
  --icon-bg-start: #f3e8ff;
  --icon-bg-end: #e9d5ff;
}

.dark .kpi-icon-purple {
  --icon-bg-start: #581c87;
  --icon-bg-end: #7c3aed;
}

.kpi-icon-orange {
  --icon-bg-start: #fed7aa;
  --icon-bg-end: #fdba74;
}

.dark .kpi-icon-orange {
  --icon-bg-start: #9a3412;
  --icon-bg-end: #ea580c;
}

/* ===== TYPOGRAPHY ===== */
.dashboard-title {
  @apply text-4xl font-bold tracking-tight;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.dark .dashboard-title {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-value {
  @apply text-3xl font-bold tracking-tight;
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.metric-label {
  @apply text-sm font-semibold text-gray-600 dark:text-gray-400;
  text-transform: uppercase;
  letter-spacing: 0.075em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.metric-subtitle {
  @apply text-xs text-gray-500 dark:text-gray-500;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Premium Typography Hierarchy */
.section-title {
  @apply text-xl font-bold text-gray-900 dark:text-white tracking-tight;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.section-subtitle {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
  letter-spacing: 0.025em;
}

.card-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.card-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
  line-height: 1.5;
}

/* Number formatting */
.number-display {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
}

/* Status text styling */
.status-text {
  @apply font-medium;
  font-variant: small-caps;
  letter-spacing: 0.05em;
}

/* ===== PROGRESS INDICATORS ===== */
.progress-ring {
  transform: rotate(-90deg);
  transition: all 0.5s ease;
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.5s ease;
  stroke-linecap: round;
}

.status-indicator {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  animation: pulse-subtle 2s infinite;
}

@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.status-completed {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

.status-processing {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

.status-pending {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

.status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

/* ===== INTERACTIVE ELEMENTS ===== */
.interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
  background: rgba(31, 41, 55, 0.1);
  border: 1px solid rgba(75, 85, 99, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 640px) {
  .dashboard-title {
    @apply text-2xl;
  }
  
  .metric-value {
    @apply text-2xl;
  }
  
  .premium-card {
    @apply p-4;
  }
  
  .kpi-card {
    @apply p-4;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .premium-card,
  .kpi-icon-container,
  .interactive-element {
    transition: none;
  }
  
  .animate-slide-in-up,
  .animate-fade-in-scale {
    animation: none;
  }
  
  .status-indicator {
    animation: none;
  }
}

/* ===== FOCUS STATES ===== */
.premium-card:focus-within {
  @apply ring-2 ring-blue-500 ring-opacity-50;
  outline: none;
}

.interactive-element:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
  outline: none;
}
