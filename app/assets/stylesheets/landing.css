/* Landing Page Specific Styles */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-pattern {
  background-image: 
    radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2px, transparent 0);
  background-size: 100px 100px;
}

.fade-in {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-delay-1 { animation-delay: 0.2s; }
.fade-in-delay-2 { animation-delay: 0.4s; }
.fade-in-delay-3 { animation-delay: 0.6s; }

.floating {
  animation: float 6s ease-in-out infinite;
}

.pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Navigation enhancements */
.nav-blur {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Button enhancements */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Feature grid enhancements */
.feature-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
}
