@import "tailwindcss";

html, body {
  height: 100%;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Back to top button enhancement */
#back-to-top {
  backdrop-filter: blur(8px);
}

/* Newsletter form styling */
.newsletter-success {
  background-color: #10b981;
  color: white;
}

/* Custom scrollbar for newsletter */
.newsletter-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}


@theme {
  --font-family-sans: "Inter", system-ui, sans-serif;
  
  --color-brand-50: #eff6ff;
  --color-brand-100: #dbeafe;
  --color-brand-500: #3b82f6;
  --color-brand-600: #2563eb;
  --color-brand-700: #1d4ed8;
  --color-brand-900: #1e3a8a;
  
  --animate-float: float 6s ease-in-out infinite;
  --animate-fade-in-up: fadeInUp 0.8s ease-out forwards;
  --animate-pulse-slow: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom utility classes using Tailwind v4 @utility directive */
@utility gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

@utility hero-pattern {
  background-image: 
    radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2px, transparent 0);
  background-size: 100px 100px;
}

@utility fade-in {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@utility fade-in-delay-1 { animation-delay: 0.2s; }
@utility fade-in-delay-2 { animation-delay: 0.4s; }
@utility fade-in-delay-3 { animation-delay: 0.6s; }

@utility floating {
  animation: float 6s ease-in-out infinite;
}

@utility pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===== PREMIUM DASHBOARD STYLES ===== */

/* Dashboard Layout */
@utility dashboard-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* Premium Cards */
@utility premium-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.premium-card:hover {
  @apply shadow-lg border-gray-200 dark:border-gray-600;
  transform: translateY(-2px);
}

.premium-card:hover::before {
  opacity: 1;
}

/* KPI Cards */
@utility kpi-card {
  @apply premium-card p-6;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  border: 1px solid rgba(255,255,255,0.2);
}

@utility kpi-icon-container {
  @apply p-3 rounded-xl;
  background: linear-gradient(135deg, var(--icon-bg-start), var(--icon-bg-end));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kpi-card:hover .kpi-icon-container {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Icon color variants */
@utility kpi-icon-blue {
  --icon-bg-start: #dbeafe;
  --icon-bg-end: #bfdbfe;
}

@utility kpi-icon-green {
  --icon-bg-start: #dcfce7;
  --icon-bg-end: #bbf7d0;
}

@utility kpi-icon-purple {
  --icon-bg-start: #f3e8ff;
  --icon-bg-end: #e9d5ff;
}

@utility kpi-icon-orange {
  --icon-bg-start: #fed7aa;
  --icon-bg-end: #fdba74;
}

/* Typography */
@utility dashboard-title {
  @apply text-4xl font-bold tracking-tight;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@utility metric-value {
  @apply text-3xl font-bold tracking-tight;
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@utility metric-label {
  @apply text-sm font-semibold text-gray-600 dark:text-gray-400;
  text-transform: uppercase;
  letter-spacing: 0.075em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@utility metric-subtitle {
  @apply text-xs text-gray-500 dark:text-gray-500;
  font-weight: 500;
  letter-spacing: 0.025em;
}

@utility section-title {
  @apply text-xl font-bold text-gray-900 dark:text-white tracking-tight;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@utility number-display {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
}

/* Status Indicators */
@utility status-indicator {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  animation: pulse-subtle 2s infinite;
}

@utility status-completed {
  @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400;
}

@utility status-processing {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400;
}

@utility status-pending {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

@utility status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

/* Interactive Elements */
@utility interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@utility animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

@utility animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

@utility loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode adjustments */
.dark .dashboard-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.dark .premium-card {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.3);
}

.dark .kpi-card {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(55, 65, 81, 0.7) 100%);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.dark .dashboard-title {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .kpi-icon-blue {
  --icon-bg-start: #1e3a8a;
  --icon-bg-end: #1d4ed8;
}

.dark .kpi-icon-green {
  --icon-bg-start: #14532d;
  --icon-bg-end: #166534;
}

.dark .kpi-icon-purple {
  --icon-bg-start: #581c87;
  --icon-bg-end: #7c3aed;
}

.dark .kpi-icon-orange {
  --icon-bg-start: #9a3412;
  --icon-bg-end: #ea580c;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}
