import { Controller } from "@hotwired/stimulus"
import Chart from "chart.js"

// Connects to data-controller="chart"
export default class extends Controller {
  static targets = ["canvas"]
  static values = { 
    type: String,
    data: Object,
    options: Object
  }

  connect() {
    this.initializeChart()
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy()
    }
  }

  initializeChart() {
    const ctx = this.canvasTarget.getContext('2d')
    
    // Default options for premium styling
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          padding: 12
        }
      },
      scales: this.getScaleOptions(),
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }

    // Merge with custom options
    const options = this.mergeOptions(defaultOptions, this.optionsValue)

    this.chart = new Chart(ctx, {
      type: this.typeValue,
      data: this.processChartData(this.dataValue),
      options: options
    })
  }

  getScaleOptions() {
    if (this.typeValue === 'doughnut' || this.typeValue === 'pie') {
      return {}
    }

    return {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderColor: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280'
        }
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderColor: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280'
        },
        beginAtZero: true
      }
    }
  }

  processChartData(data) {
    // Add premium color schemes based on chart type
    if (data.datasets) {
      data.datasets = data.datasets.map((dataset, index) => {
        return {
          ...dataset,
          ...this.getDatasetStyling(dataset, index)
        }
      })
    }
    return data
  }

  getDatasetStyling(dataset, index) {
    const colorSchemes = [
      {
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderColor: 'rgb(59, 130, 246)',
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff'
      },
      {
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderColor: 'rgb(16, 185, 129)',
        pointBackgroundColor: 'rgb(16, 185, 129)',
        pointBorderColor: '#fff'
      },
      {
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        borderColor: 'rgb(245, 158, 11)',
        pointBackgroundColor: 'rgb(245, 158, 11)',
        pointBorderColor: '#fff'
      },
      {
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        borderColor: 'rgb(139, 92, 246)',
        pointBackgroundColor: 'rgb(139, 92, 246)',
        pointBorderColor: '#fff'
      }
    ]

    const scheme = colorSchemes[index % colorSchemes.length]

    // Special handling for doughnut/pie charts
    if (this.typeValue === 'doughnut' || this.typeValue === 'pie') {
      return {
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)'
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(245, 158, 11)',
          'rgb(239, 68, 68)',
          'rgb(139, 92, 246)',
          'rgb(236, 72, 153)'
        ],
        borderWidth: 2,
        hoverBorderWidth: 3
      }
    }

    return {
      ...scheme,
      borderWidth: 2,
      pointRadius: 4,
      pointHoverRadius: 6,
      pointBorderWidth: 2,
      tension: 0.4,
      fill: dataset.fill !== undefined ? dataset.fill : true
    }
  }

  mergeOptions(defaults, custom) {
    if (!custom) return defaults
    
    // Deep merge options
    const merged = { ...defaults }
    
    Object.keys(custom).forEach(key => {
      if (typeof custom[key] === 'object' && custom[key] !== null && !Array.isArray(custom[key])) {
        merged[key] = { ...merged[key], ...custom[key] }
      } else {
        merged[key] = custom[key]
      }
    })
    
    return merged
  }

  // Method to update chart data (for real-time updates)
  updateData(newData) {
    if (this.chart) {
      this.chart.data = this.processChartData(newData)
      this.chart.update('active')
    }
  }

  // Method to update chart options
  updateOptions(newOptions) {
    if (this.chart) {
      this.chart.options = this.mergeOptions(this.chart.options, newOptions)
      this.chart.update()
    }
  }

  // Utility method to format numbers for display
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // Utility method to format percentages
  formatPercentage(num) {
    return num.toFixed(1) + '%'
  }
}
