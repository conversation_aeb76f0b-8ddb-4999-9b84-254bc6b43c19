import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="dashboard"
export default class extends Controller {
  static targets = ["overview", "processing", "activity", "billing", "performance"]
  static values = { 
    refreshInterval: { type: Number, default: 30000 },
    autoRefresh: { type: Boolean, default: true }
  }

  connect() {
    console.log("Dashboard controller connected")
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
    
    // Add event listeners for manual refresh
    this.addRefreshListeners()
  }

  disconnect() {
    this.stopAutoRefresh()
  }

  startAutoRefresh() {
    this.stopAutoRefresh() // Clear any existing interval
    
    this.refreshTimer = setInterval(() => {
      this.refreshAllSections()
    }, this.refreshIntervalValue)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  toggleAutoRefresh() {
    this.autoRefreshValue = !this.autoRefreshValue
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    } else {
      this.stopAutoRefresh()
    }
  }

  refreshAllSections() {
    this.refreshOverview()
    this.refreshProcessing()
    this.refreshActivity()
    this.refreshBilling()
    this.refreshPerformance()
  }

  refreshOverview() {
    this.refreshSection('overview')
  }

  refreshProcessing() {
    this.refreshSection('document_processing')
  }

  refreshActivity() {
    this.refreshSection('user_activity')
  }

  refreshBilling() {
    this.refreshSection('billing')
  }

  refreshPerformance() {
    this.refreshSection('system_performance')
  }

  async refreshSection(sectionName) {
    try {
      const url = new URL(window.location.href)
      const refreshUrl = `/dashboard/refresh/${sectionName}${url.search}`
      
      const response = await fetch(refreshUrl, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      this.updateSectionData(sectionName, data)
      
    } catch (error) {
      console.error(`Error refreshing ${sectionName}:`, error)
      this.showRefreshError(sectionName)
    }
  }

  updateSectionData(sectionName, data) {
    switch (sectionName) {
      case 'overview':
        this.updateOverviewMetrics(data.overview)
        break
      case 'document_processing':
        this.updateProcessingMetrics(data.document_processing)
        break
      case 'user_activity':
        this.updateActivityMetrics(data.user_activity)
        break
      case 'billing':
        this.updateBillingMetrics(data.billing)
        break
      case 'system_performance':
        this.updatePerformanceMetrics(data.system_performance)
        break
    }
    
    this.showRefreshSuccess(sectionName)
  }

  updateOverviewMetrics(metrics) {
    if (!this.hasOverviewTarget) return
    
    // Update primary KPI cards
    this.updateMetricCard('total_documents', metrics.total_documents)
    this.updateMetricCard('success_rate', `${metrics.success_rate}%`)
    this.updateMetricCard('active_users', metrics.active_users)
    this.updateMetricCard('avg_processing_time', this.formatDuration(metrics.avg_processing_time))
  }

  updateProcessingMetrics(metrics) {
    // Update processing status indicators
    this.updateStatusIndicator('completed', metrics.total_processed)
    this.updateStatusIndicator('processing', metrics.processing)
    this.updateStatusIndicator('pending', metrics.pending)
    this.updateStatusIndicator('failed', metrics.failed)
  }

  updateActivityMetrics(metrics) {
    // Update user activity metrics
    this.updateActivityIndicator('active_today', metrics.active_users_today)
    this.updateActivityIndicator('active_week', metrics.active_users_this_week)
    this.updateActivityIndicator('uploads_today', metrics.documents_uploaded_today)
    this.updateActivityIndicator('engagement_rate', `${metrics.user_engagement.engagement_rate}%`)
  }

  updateBillingMetrics(metrics) {
    if (!metrics || Object.keys(metrics).length === 0) return
    
    // Update billing indicators
    this.updateBillingIndicator('plan_name', metrics.plan_name)
    this.updateBillingIndicator('monthly_revenue', this.formatCurrency(metrics.monthly_revenue))
    this.updateBillingIndicator('documents_used', `${metrics.usage_vs_limits.documents_used}/${metrics.usage_vs_limits.documents_limit}`)
    this.updateBillingIndicator('cost_per_document', this.formatCurrency(metrics.cost_per_document))
  }

  updatePerformanceMetrics(metrics) {
    // Update system performance indicators
    this.updatePerformanceIndicator('queue_length', metrics.queue_stats.total_pending || 0)
    this.updatePerformanceIndicator('processing_rate', `${(metrics.queue_stats.processing_rate || 0).toFixed(1)}/min`)
    this.updatePerformanceIndicator('error_rate', `${metrics.error_rates.error_rate}%`)
    this.updatePerformanceIndicator('api_response', `${metrics.api_performance.avg_response_time}ms`)
  }

  updateMetricCard(metricName, value) {
    const element = this.element.querySelector(`[data-metric="${metricName}"]`)
    if (element) {
      element.textContent = value
      this.animateUpdate(element)
    }
  }

  updateStatusIndicator(status, value) {
    const element = this.element.querySelector(`[data-status="${status}"]`)
    if (element) {
      element.textContent = this.formatNumber(value)
      this.animateUpdate(element)
    }
  }

  updateActivityIndicator(activity, value) {
    const element = this.element.querySelector(`[data-activity="${activity}"]`)
    if (element) {
      element.textContent = value
      this.animateUpdate(element)
    }
  }

  updateBillingIndicator(billing, value) {
    const element = this.element.querySelector(`[data-billing="${billing}"]`)
    if (element) {
      element.textContent = value
      this.animateUpdate(element)
    }
  }

  updatePerformanceIndicator(performance, value) {
    const element = this.element.querySelector(`[data-performance="${performance}"]`)
    if (element) {
      element.textContent = value
      this.animateUpdate(element)
    }
  }

  animateUpdate(element) {
    element.classList.add('animate-pulse')
    setTimeout(() => {
      element.classList.remove('animate-pulse')
    }, 1000)
  }

  showRefreshSuccess(sectionName) {
    // Show a subtle success indicator
    const indicator = this.element.querySelector(`[data-refresh-indicator="${sectionName}"]`)
    if (indicator) {
      indicator.classList.add('text-green-500')
      setTimeout(() => {
        indicator.classList.remove('text-green-500')
      }, 2000)
    }
  }

  showRefreshError(sectionName) {
    // Show a subtle error indicator
    const indicator = this.element.querySelector(`[data-refresh-indicator="${sectionName}"]`)
    if (indicator) {
      indicator.classList.add('text-red-500')
      setTimeout(() => {
        indicator.classList.remove('text-red-500')
      }, 5000)
    }
  }

  addRefreshListeners() {
    // Add click listeners for manual refresh buttons
    const refreshButtons = this.element.querySelectorAll('[data-action*="dashboard#refresh"]')
    refreshButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault()
        const section = button.dataset.section
        if (section) {
          this.refreshSection(section)
        } else {
          this.refreshAllSections()
        }
      })
    })
  }

  // Utility methods for formatting
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`
    } else if (seconds < 3600) {
      return `${(seconds / 60).toFixed(1)}m`
    } else {
      return `${(seconds / 3600).toFixed(1)}h`
    }
  }

  // Date range change handler
  dateRangeChanged(event) {
    // Reload the page with new date range
    const form = event.target.closest('form')
    if (form) {
      form.submit()
    }
  }
}
