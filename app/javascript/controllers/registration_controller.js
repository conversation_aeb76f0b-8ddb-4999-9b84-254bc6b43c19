import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="registration"
export default class extends Controller {
  static targets = ["subdomain", "subdomainPreview"]
  
  connect() {
    console.log("Registration controller connected")
  }
  
  normalizeSubdomain() {
    if (this.hasSubdomainTarget) {
      const input = this.subdomainTarget
      let value = input.value
      
      // Convert to lowercase and remove non-alphanumeric characters
      value = value.toLowerCase().replace(/[^a-z0-9]/g, '')
      
      // Update the input value
      input.value = value
      
      // Update preview if it exists
      if (this.hasSubdomainPreviewTarget) {
        this.subdomainPreviewTarget.textContent = value ? `${value}.docutiz.com` : 'your-subdomain.docutiz.com'
      }
    }
  }
}
