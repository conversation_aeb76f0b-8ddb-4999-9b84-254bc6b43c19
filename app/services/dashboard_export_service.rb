# Service for exporting dashboard data in various formats
class DashboardExportService
  include ActionView::Helpers::NumberHelper
  
  EXPORT_FORMATS = %w[csv excel pdf json].freeze
  
  def initialize(tenant, user, date_range = nil)
    @tenant = tenant
    @user = user
    @date_range = date_range || default_date_range
    @analytics_service = DashboardAnalyticsService.for_tenant(@tenant, @user, @date_range)
  end
  
  def export(format)
    raise ArgumentError, "Invalid format: #{format}" unless EXPORT_FORMATS.include?(format.to_s)
    
    case format.to_s
    when 'csv'
      export_to_csv
    when 'excel'
      export_to_excel
    when 'pdf'
      export_to_pdf
    when 'json'
      export_to_json
    end
  end
  
  def export_to_csv
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      # Add header row
      csv << csv_headers
      
      # Add overview metrics
      overview_data.each { |row| csv << row }
      
      # Add separator
      csv << []
      csv << ['Document Processing Metrics']
      csv << []
      
      # Add document processing data
      document_processing_data.each { |row| csv << row }
      
      # Add separator
      csv << []
      csv << ['User Activity Metrics']
      csv << []
      
      # Add user activity data
      user_activity_data.each { |row| csv << row }
      
      # Add historical trends if available
      if historical_data.any?
        csv << []
        csv << ['Historical Trends (Last 30 Days)']
        csv << ['Date', 'Documents', 'Success Rate', 'Active Users', 'Processing Time']
        
        historical_data.each do |day_data|
          csv << [
            day_data[:date],
            day_data[:documents],
            "#{day_data[:success_rate]}%",
            day_data[:active_users],
            "#{day_data[:processing_time]}s"
          ]
        end
      end
    end
  end
  
  def export_to_excel
    require 'rubyXL'
    
    workbook = RubyXL::Workbook.new
    
    # Overview sheet
    overview_sheet = workbook[0]
    overview_sheet.sheet_name = 'Overview'
    add_overview_to_sheet(overview_sheet)
    
    # Document processing sheet
    doc_sheet = workbook.add_worksheet('Document Processing')
    add_document_processing_to_sheet(doc_sheet)
    
    # User activity sheet
    user_sheet = workbook.add_worksheet('User Activity')
    add_user_activity_to_sheet(user_sheet)
    
    # Historical trends sheet
    if historical_data.any?
      trends_sheet = workbook.add_worksheet('Historical Trends')
      add_historical_trends_to_sheet(trends_sheet)
    end
    
    # Return the workbook as a string
    workbook.stream.string
  end
  
  def export_to_pdf
    require 'prawn'
    
    Prawn::Document.new do |pdf|
      # Title
      pdf.text "Dashboard Report - #{@tenant.name}", size: 24, style: :bold
      pdf.text "Generated on #{Date.current.strftime('%B %d, %Y')}", size: 12
      pdf.text "Period: #{format_date_range}", size: 12
      pdf.move_down 20
      
      # Overview section
      pdf.text "Overview", size: 18, style: :bold
      pdf.move_down 10
      
      overview_metrics = @analytics_service.overview_metrics
      pdf.text "Total Documents: #{number_with_delimiter(overview_metrics[:total_documents])}"
      pdf.text "Success Rate: #{overview_metrics[:success_rate]}%"
      pdf.text "Active Users: #{number_with_delimiter(overview_metrics[:active_users])}"
      pdf.text "Average Processing Time: #{overview_metrics[:avg_processing_time]}s"
      pdf.move_down 20
      
      # Document processing section
      pdf.text "Document Processing", size: 18, style: :bold
      pdf.move_down 10
      
      doc_metrics = @analytics_service.document_processing_metrics
      pdf.text "Processed: #{number_with_delimiter(doc_metrics[:total_processed])}"
      pdf.text "Failed: #{number_with_delimiter(doc_metrics[:failed])}"
      pdf.text "Pending: #{number_with_delimiter(doc_metrics[:pending])}"
      pdf.text "Processing: #{number_with_delimiter(doc_metrics[:processing])}"
      pdf.move_down 20
      
      # User activity section
      pdf.text "User Activity", size: 18, style: :bold
      pdf.move_down 10
      
      user_metrics = @analytics_service.user_activity_metrics
      pdf.text "Active Today: #{number_with_delimiter(user_metrics[:active_users_today])}"
      pdf.text "Active This Week: #{number_with_delimiter(user_metrics[:active_users_this_week])}"
      pdf.text "Documents Uploaded Today: #{number_with_delimiter(user_metrics[:documents_uploaded_today])}"
      
      # Add billing information if user can view it
      if @user.can_manage_billing?
        pdf.move_down 20
        pdf.text "Billing & Revenue", size: 18, style: :bold
        pdf.move_down 10
        
        billing_metrics = @analytics_service.billing_metrics
        pdf.text "Monthly Revenue: #{number_to_currency(billing_metrics[:monthly_revenue])}"
        pdf.text "Current Plan: #{billing_metrics[:plan_name]}"
        pdf.text "Documents Used: #{billing_metrics[:usage_vs_limits][:documents_used]}/#{billing_metrics[:usage_vs_limits][:documents_limit]}"
      end
      
      # Footer
      pdf.move_down 30
      pdf.text "Generated by Docutiz Dashboard", size: 10, style: :italic
    end.render
  end
  
  def export_to_json
    {
      tenant: {
        name: @tenant.name,
        subdomain: @tenant.subdomain,
        plan: @tenant.plan
      },
      export_info: {
        generated_at: Time.current.iso8601,
        generated_by: @user.name,
        date_range: {
          start: @date_range[:start_date].iso8601,
          end: @date_range[:end_date].iso8601
        }
      },
      metrics: @analytics_service.dashboard_metrics,
      historical_data: historical_data
    }.to_json
  end
  
  def filename(format)
    timestamp = Time.current.strftime('%Y%m%d_%H%M%S')
    "#{@tenant.subdomain}_dashboard_#{timestamp}.#{format}"
  end
  
  private
  
  def default_date_range
    {
      start_date: 30.days.ago.to_date,
      end_date: Date.current
    }
  end
  
  def csv_headers
    ['Metric', 'Value', 'Description']
  end
  
  def overview_data
    overview = @analytics_service.overview_metrics
    [
      ['Total Documents', number_with_delimiter(overview[:total_documents]), 'Total documents processed in period'],
      ['Success Rate', "#{overview[:success_rate]}%", 'Percentage of successfully processed documents'],
      ['Active Users', number_with_delimiter(overview[:active_users]), 'Number of active users in period'],
      ['Avg Processing Time', "#{overview[:avg_processing_time]}s", 'Average time to process a document']
    ]
  end
  
  def document_processing_data
    doc_metrics = @analytics_service.document_processing_metrics
    [
      ['Processed Documents', number_with_delimiter(doc_metrics[:total_processed]), 'Successfully processed documents'],
      ['Failed Documents', number_with_delimiter(doc_metrics[:failed]), 'Documents that failed processing'],
      ['Pending Documents', number_with_delimiter(doc_metrics[:pending]), 'Documents waiting to be processed'],
      ['Currently Processing', number_with_delimiter(doc_metrics[:processing]), 'Documents currently being processed']
    ]
  end
  
  def user_activity_data
    user_metrics = @analytics_service.user_activity_metrics
    [
      ['Active Today', number_with_delimiter(user_metrics[:active_users_today]), 'Users active today'],
      ['Active This Week', number_with_delimiter(user_metrics[:active_users_this_week]), 'Users active this week'],
      ['Uploads Today', number_with_delimiter(user_metrics[:documents_uploaded_today]), 'Documents uploaded today'],
      ['Engagement Rate', "#{user_metrics[:user_engagement][:engagement_rate]}%", 'User engagement percentage']
    ]
  end
  
  def historical_data
    @historical_data ||= DashboardMetric
      .where(tenant: @tenant)
      .where(time_period: 'daily')
      .where(recorded_at: @date_range[:start_date]..@date_range[:end_date])
      .group(:recorded_at)
      .group(:metric_type)
      .pluck(:recorded_at, :metric_type, :value)
      .group_by { |date, _, _| date.to_date }
      .map do |date, metrics|
        metrics_hash = metrics.to_h { |_, type, value| [type, value] }
        {
          date: date.strftime('%Y-%m-%d'),
          documents: metrics_hash['document_count'] || 0,
          success_rate: metrics_hash['success_rate'] || 0,
          active_users: metrics_hash['user_activity'] || 0,
          processing_time: metrics_hash['processing_time'] || 0
        }
      end
      .sort_by { |data| data[:date] }
  end
  
  def format_date_range
    "#{@date_range[:start_date].strftime('%B %d, %Y')} - #{@date_range[:end_date].strftime('%B %d, %Y')}"
  end
  
  def add_overview_to_sheet(sheet)
    sheet.add_cell(0, 0, 'Dashboard Overview')
    sheet.add_cell(1, 0, 'Metric')
    sheet.add_cell(1, 1, 'Value')
    
    overview_data.each_with_index do |row, index|
      sheet.add_cell(index + 2, 0, row[0])
      sheet.add_cell(index + 2, 1, row[1])
    end
  end
  
  def add_document_processing_to_sheet(sheet)
    sheet.add_cell(0, 0, 'Document Processing Metrics')
    sheet.add_cell(1, 0, 'Metric')
    sheet.add_cell(1, 1, 'Value')
    
    document_processing_data.each_with_index do |row, index|
      sheet.add_cell(index + 2, 0, row[0])
      sheet.add_cell(index + 2, 1, row[1])
    end
  end
  
  def add_user_activity_to_sheet(sheet)
    sheet.add_cell(0, 0, 'User Activity Metrics')
    sheet.add_cell(1, 0, 'Metric')
    sheet.add_cell(1, 1, 'Value')
    
    user_activity_data.each_with_index do |row, index|
      sheet.add_cell(index + 2, 0, row[0])
      sheet.add_cell(index + 2, 1, row[1])
    end
  end
  
  def add_historical_trends_to_sheet(sheet)
    sheet.add_cell(0, 0, 'Historical Trends')
    headers = ['Date', 'Documents', 'Success Rate', 'Active Users', 'Processing Time']
    headers.each_with_index { |header, index| sheet.add_cell(1, index, header) }
    
    historical_data.each_with_index do |day_data, index|
      sheet.add_cell(index + 2, 0, day_data[:date])
      sheet.add_cell(index + 2, 1, day_data[:documents])
      sheet.add_cell(index + 2, 2, "#{day_data[:success_rate]}%")
      sheet.add_cell(index + 2, 3, day_data[:active_users])
      sheet.add_cell(index + 2, 4, "#{day_data[:processing_time]}s")
    end
  end
end
