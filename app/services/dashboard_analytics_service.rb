class DashboardAnalyticsService
  def initialize(tenant, user = nil, date_range = nil)
    @tenant = tenant
    @user = user
    @date_range = date_range || default_date_range
  end

  def self.for_tenant(tenant, user = nil, date_range = nil)
    new(tenant, user, date_range)
  end

  # Main dashboard metrics
  def dashboard_metrics
    Rails.cache.fetch(cache_key("dashboard_metrics"), expires_in: 5.minutes) do
      {
        overview: overview_metrics,
        document_processing: document_processing_metrics,
        user_activity: user_activity_metrics,
        billing: billing_metrics,
        system_performance: system_performance_metrics,
        trends: trend_metrics
      }
    end
  end

  # Overview metrics for the top of dashboard
  def overview_metrics
    {
      total_documents: @tenant.documents.count,
      documents_this_period: documents_in_range.count,
      total_templates: @tenant.extraction_templates.count,
      active_templates: @tenant.extraction_templates.active.count,
      total_users: @tenant.users.count,
      active_users: active_users_count,
      success_rate: overall_success_rate,
      avg_processing_time: average_processing_time
    }
  end

  # Document processing analytics
  def document_processing_metrics
    docs_in_range = documents_in_range
    
    {
      total_processed: docs_in_range.completed.count,
      pending: @tenant.documents.pending.count,
      processing: @tenant.documents.processing.count,
      failed: docs_in_range.failed.count,
      requires_review: docs_in_range.requires_review.count,
      success_rate: calculate_success_rate(docs_in_range),
      failure_rate: calculate_failure_rate(docs_in_range),
      avg_processing_time: calculate_avg_processing_time(docs_in_range),
      processing_volume_by_day: processing_volume_by_day,
      status_distribution: status_distribution,
      template_usage: template_usage_stats,
      ai_model_performance: ai_model_performance_stats
    }
  end

  # User activity metrics
  def user_activity_metrics
    {
      active_users_today: active_users_today,
      active_users_this_week: active_users_this_week,
      documents_uploaded_today: documents_uploaded_today,
      documents_uploaded_this_week: documents_uploaded_this_week,
      top_uploaders: top_document_uploaders,
      user_engagement: user_engagement_stats,
      activity_timeline: recent_activity_timeline
    }
  end

  # Billing and subscription metrics
  def billing_metrics
    return {} unless @user&.can_manage_billing?

    {
      subscription_status: @tenant.subscription&.status,
      plan_name: @tenant.plan_name || "Free",
      monthly_revenue: calculate_monthly_revenue,
      usage_vs_limits: usage_vs_limits_stats,
      billing_period_usage: billing_period_usage,
      cost_per_document: calculate_cost_per_document,
      upgrade_recommendations: upgrade_recommendations
    }
  end

  # System performance metrics
  def system_performance_metrics
    {
      queue_stats: queue_performance_stats,
      api_performance: api_performance_stats,
      error_rates: error_rate_stats,
      processing_efficiency: processing_efficiency_stats
    }
  end

  # Trend analysis
  def trend_metrics
    {
      document_trends: document_volume_trends,
      success_rate_trends: success_rate_trends,
      user_growth_trends: user_growth_trends,
      performance_trends: performance_trends
    }
  end

  private

  def default_date_range
    30.days.ago.beginning_of_day..Time.current.end_of_day
  end

  def documents_in_range
    @documents_in_range ||= @tenant.documents.where(created_at: @date_range)
  end

  def cache_key(suffix)
    "dashboard_analytics:#{@tenant.id}:#{@date_range.first.to_date}:#{@date_range.last.to_date}:#{suffix}"
  end

  def overall_success_rate
    total_docs = @tenant.documents.count
    return 0 if total_docs.zero?
    
    successful_docs = @tenant.documents.where(status: ['completed', 'approved']).count
    (successful_docs.to_f / total_docs * 100).round(1)
  end

  def average_processing_time
    completed_docs = @tenant.documents.completed
                            .where.not(processing_started_at: nil)
                            .where.not(processing_completed_at: nil)
    
    return 0 if completed_docs.empty?
    
    total_time = completed_docs.sum do |doc|
      doc.processing_completed_at - doc.processing_started_at
    end
    
    (total_time / completed_docs.count).round(2)
  end

  def active_users_count
    @tenant.users.joins(:documents)
           .where(documents: { created_at: @date_range })
           .distinct
           .count
  end

  def calculate_success_rate(docs)
    return 0 if docs.empty?
    successful = docs.where(status: ['completed', 'approved']).count
    (successful.to_f / docs.count * 100).round(1)
  end

  def calculate_failure_rate(docs)
    return 0 if docs.empty?
    failed = docs.failed.count
    (failed.to_f / docs.count * 100).round(1)
  end

  def calculate_avg_processing_time(docs)
    completed = docs.completed
                   .where.not(processing_started_at: nil)
                   .where.not(processing_completed_at: nil)
    
    return 0 if completed.empty?
    
    total_time = completed.sum do |doc|
      doc.processing_completed_at - doc.processing_started_at
    end
    
    (total_time / completed.count).round(2)
  end

  def processing_volume_by_day
    documents_in_range
      .group("DATE(created_at)")
      .count
      .transform_keys { |date| date.strftime("%Y-%m-%d") }
  end

  def status_distribution
    documents_in_range
      .group(:status)
      .count
  end

  def template_usage_stats
    return {} unless @tenant.extraction_templates.any?

    @tenant.extraction_templates
           .joins(:documents)
           .where(documents: { created_at: @date_range })
           .group(:name)
           .count
           .sort_by { |_, count| -count }
           .first(10)
           .to_h
  rescue => e
    Rails.logger.error "Error calculating template usage stats: #{e.message}"
    {}
  end

  def ai_model_performance_stats
    @tenant.documents
           .where(created_at: @date_range)
           .where.not(assigned_model: nil)
           .group(:assigned_model)
           .group(:status)
           .count
  end

  def active_users_today
    @tenant.users.joins(:documents)
           .where(documents: { created_at: Time.current.beginning_of_day..Time.current.end_of_day })
           .distinct
           .count
  end

  def active_users_this_week
    @tenant.users.joins(:documents)
           .where(documents: { created_at: Time.current.beginning_of_week..Time.current.end_of_week })
           .distinct
           .count
  end

  def documents_uploaded_today
    @tenant.documents.where(created_at: Time.current.beginning_of_day..Time.current.end_of_day).count
  end

  def documents_uploaded_this_week
    @tenant.documents.where(created_at: Time.current.beginning_of_week..Time.current.end_of_week).count
  end

  def top_document_uploaders
    @tenant.users
           .joins(:documents)
           .where(documents: { created_at: @date_range })
           .group('users.id', 'users.name')
           .count
           .sort_by { |_, count| -count }
           .first(5)
           .map { |(id, name), count| { user_id: id, name: name, count: count } }
  end

  def user_engagement_stats
    total_users = @tenant.users.count
    active_users = active_users_count
    
    {
      total_users: total_users,
      active_users: active_users,
      engagement_rate: total_users > 0 ? (active_users.to_f / total_users * 100).round(1) : 0
    }
  end

  def recent_activity_timeline
    @tenant.activities
           .includes(:user, :trackable)
           .where(created_at: @date_range)
           .order(created_at: :desc)
           .limit(20)
           .map do |activity|
      {
        id: activity.id,
        action: activity.action,
        description: activity.description,
        user_name: activity.user.name,
        created_at: activity.created_at,
        trackable_type: activity.trackable_type,
        trackable_id: activity.trackable_id
      }
    end
  end

  def calculate_monthly_revenue
    return 0 unless @tenant.subscription&.active?
    
    # This would integrate with Stripe to get actual revenue data
    # For now, return estimated based on plan
    case @tenant.plan_name.downcase
    when 'basic' then 29
    when 'professional' then 99
    when 'enterprise' then 299
    else 0
    end
  end

  def usage_vs_limits_stats
    {
      documents_used: @tenant.documents.where(created_at: Time.current.beginning_of_month..Time.current.end_of_month).count,
      documents_limit: @tenant.plan_limit('documents_per_month'),
      users_used: @tenant.users.count,
      users_limit: @tenant.plan_limit('team_members'),
      templates_used: @tenant.extraction_templates.count,
      templates_limit: @tenant.plan_limit('custom_templates')
    }
  end

  def billing_period_usage
    start_date = Time.current.beginning_of_month
    end_date = Time.current.end_of_month
    
    @tenant.documents
           .where(created_at: start_date..end_date)
           .group("DATE(created_at)")
           .count
           .transform_keys { |date| date.strftime("%Y-%m-%d") }
  end

  def calculate_cost_per_document
    monthly_revenue = calculate_monthly_revenue
    monthly_docs = @tenant.documents.where(created_at: Time.current.beginning_of_month..Time.current.end_of_month).count
    
    return 0 if monthly_docs.zero?
    (monthly_revenue.to_f / monthly_docs).round(2)
  end

  def upgrade_recommendations
    usage = usage_vs_limits_stats
    recommendations = []
    
    if usage[:documents_used] >= usage[:documents_limit] * 0.8
      recommendations << "Consider upgrading for more document processing capacity"
    end
    
    if usage[:users_used] >= usage[:users_limit]
      recommendations << "Upgrade to add more team members"
    end
    
    recommendations
  end

  def queue_performance_stats
    DocumentQueueService.queue_stats(@tenant)
  end

  def api_performance_stats
    # This would integrate with monitoring tools
    # For now, return mock data
    {
      avg_response_time: 150, # ms
      requests_per_minute: 12,
      error_rate: 0.5 # percentage
    }
  end

  def error_rate_stats
    total_docs = documents_in_range.count
    failed_docs = documents_in_range.failed.count
    
    {
      total_documents: total_docs,
      failed_documents: failed_docs,
      error_rate: total_docs > 0 ? (failed_docs.to_f / total_docs * 100).round(2) : 0
    }
  end

  def processing_efficiency_stats
    completed_docs = documents_in_range.completed
    total_processing_time = completed_docs.sum(&:processing_time).to_f
    
    {
      documents_processed: completed_docs.count,
      total_processing_time: total_processing_time,
      avg_processing_time: completed_docs.count > 0 ? (total_processing_time / completed_docs.count).round(2) : 0
    }
  end

  def document_volume_trends
    # Get daily document counts for the last 30 days
    30.days.ago.to_date.upto(Date.current).map do |date|
      {
        date: date.strftime("%Y-%m-%d"),
        count: @tenant.documents.where(created_at: date.beginning_of_day..date.end_of_day).count
      }
    end
  end

  def success_rate_trends
    # Get daily success rates for the last 30 days
    30.days.ago.to_date.upto(Date.current).map do |date|
      daily_docs = @tenant.documents.where(created_at: date.beginning_of_day..date.end_of_day)
      success_rate = calculate_success_rate(daily_docs)
      
      {
        date: date.strftime("%Y-%m-%d"),
        success_rate: success_rate
      }
    end
  end

  def user_growth_trends
    # Get daily user counts for the last 30 days
    30.days.ago.to_date.upto(Date.current).map do |date|
      {
        date: date.strftime("%Y-%m-%d"),
        total_users: @tenant.users.where(created_at: ..date.end_of_day).count
      }
    end
  end

  def performance_trends
    # Get daily average processing times for the last 30 days
    30.days.ago.to_date.upto(Date.current).map do |date|
      daily_docs = @tenant.documents.completed.where(created_at: date.beginning_of_day..date.end_of_day)
      avg_time = calculate_avg_processing_time(daily_docs)
      
      {
        date: date.strftime("%Y-%m-%d"),
        avg_processing_time: avg_time
      }
    end
  end
end
