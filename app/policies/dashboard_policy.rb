class DashboardPolicy < ApplicationPolicy
  attr_reader :user, :dashboard

  def initialize(user, dashboard)
    @user = user
    @dashboard = dashboard
    super(user, dashboard)
  end

  def index?
    user.present?
  end

  def refresh_section?
    index?
  end

  def view_billing_metrics?
    user&.can_manage_billing?
  end

  def view_admin_metrics?
    user&.can_manage_users?
  end

  def view_system_performance?
    user&.can_manage_users?
  end

  def view_user_activity?
    user&.can_manage_users?
  end

  def export_data?
    user&.can_manage_users?
  end

  def customize_dashboard?
    user.present?
  end

  class Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      if user&.can_manage_users?
        # Admins and owners can see all dashboard data
        scope
      else
        # Regular members see limited dashboard data
        scope.where(user: user)
      end
    end

    private

    attr_reader :user, :scope
  end
end
