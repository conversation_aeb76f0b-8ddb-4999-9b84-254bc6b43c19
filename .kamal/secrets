# Secrets defined here are available for reference under registry/password, env/secret, builder/secrets,
# and accessories/*/env/secret in config/deploy.yml. All secrets should be pulled from either
# password manager, ENV, or a file. DO NOT ENTER RAW CREDENTIALS HERE! This file needs to be safe for git.

# Docker Registry (Docker Hub or GitHub Container Registry)
KAMAL_REGISTRY_PASSWORD=$KAMAL_REGISTRY_PASSWORD

# Rails Credentials
RAILS_MASTER_KEY=$(cat config/master.key)

# Database (for external database, otherwise managed by accessory)
DATABASE_URL=$DATABASE_URL
POSTGRES_PASSWORD=$POSTGRES_PASSWORD

# AI Services
OPENAI_API_KEY=$OPENAI_API_KEY
ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY

# Stripe Payment Processing
STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY
STRIPE_PUBLISHABLE_KEY=$STRIPE_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET

# AWS S3 for File Storage
AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
AWS_BUCKET=$AWS_BUCKET

# Error Monitoring
HONEYBADGER_API_KEY=$HONEYBADGER_API_KEY

# Optional: Pull from 1Password or other password manager
# SECRETS=$(kamal secrets fetch --adapter 1password --account your-account --from Vault/Docutiz KAMAL_REGISTRY_PASSWORD RAILS_MASTER_KEY DATABASE_URL OPENAI_API_KEY ANTHROPIC_API_KEY STRIPE_SECRET_KEY AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY)
# KAMAL_REGISTRY_PASSWORD=$(kamal secrets extract KAMAL_REGISTRY_PASSWORD ${SECRETS})
# RAILS_MASTER_KEY=$(kamal secrets extract RAILS_MASTER_KEY ${SECRETS})
# DATABASE_URL=$(kamal secrets extract DATABASE_URL ${SECRETS})
# etc...